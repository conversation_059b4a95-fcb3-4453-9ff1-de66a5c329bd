/** @type {import('tailwindcss').Config} */
import plugin from 'tailwindcss/plugin';

export const darkMode = ['class'];
export const content = ['./src/**/*.{js,ts,jsx,tsx,mdx}'];
export const theme = {
  extend: {
    borderStyle: {
      DEFAULT: 'solid',
    },
    colors: {
      'grey-main': '#959DAB',
      'red-main': '#EB1648',
      'green-main': '#14A800',
      'grey-border': '#EBEBEB',
      'primary-text': '#09182C',
      'grey-light': '#F5F9FF',
      'blue-dark': '#074059',
      'roman-silver': '#828A95',
      'foundation-blue': '#1184B7',
      'carolina-blue': '#15A5E5',
      'carolina-blue-20': '#D0EDFA',
      'carolina-blue-40': '#A1DBF5',
      'carolina-blue-60': '#69C7F1',
      'english-manor': '#6D83A3',
      platinium: '#E7E7E9',
      'gray-80': '#5E6774',
      'metal-gray': '#6D7380',
      error: '#EB1648',
      disabled: '#DEE1EB',
      dark: '#222',
      background: 'hsl(var(--background))',
      foreground: 'hsl(var(--foreground))',
      card: {
        DEFAULT: 'hsl(var(--card))',
        foreground: 'hsl(var(--card-foreground))',
      },
      popover: {
        DEFAULT: 'hsl(var(--popover))',
        foreground: 'hsl(var(--popover-foreground))',
      },
      primary: {
        DEFAULT: 'hsl(var(--primary))',
        foreground: 'hsl(var(--primary-foreground))',
      },
      secondary: {
        DEFAULT: 'hsl(var(--secondary))',
        foreground: 'hsl(var(--secondary-foreground))',
      },
      muted: {
        DEFAULT: 'hsl(var(--muted))',
        foreground: 'hsl(var(--muted-foreground))',
      },
      accent: {
        DEFAULT: 'hsl(var(--accent))',
        foreground: 'hsl(var(--accent-foreground))',
      },
      destructive: {
        DEFAULT: 'hsl(var(--destructive))',
        foreground: 'hsl(var(--destructive-foreground))',
      },
      border: 'hsl(var(--border))',
      input: 'hsl(var(--input))',
      ring: 'hsl(var(--ring))',
      chart: {
        1: 'hsl(var(--chart-1))',
        2: 'hsl(var(--chart-2))',
        3: 'hsl(var(--chart-3))',
        4: 'hsl(var(--chart-4))',
        5: 'hsl(var(--chart-5))',
      },
    },
    fontFamily: {
      poppins: 'var(--font-poppins)',
    },
    container: {
      center: 'true',
      padding: {
        DEFAULT: '1rem',
        xs: '1rem',
        sm: '1.5rem',
      },
      screens: {
        sm: '600px',
        md: '728px',
        lg: '1024px',
        xl: '1200px',
      },
    },
    boxShadow: {
      header: '0px 5px 10px rgba(7, 67, 89, 0.08)',
      sliderThumb: 'rgba(21, 165, 229, 0.16) 0px 0px 0px 10px',
      'card-12': '0px 4px 12px 0px rgba(0, 0, 0, 0.12)',
      'card-25': '0px 4px 12px 0px rgba(13, 99, 137, 0.25)',
      'nav-item':
        'rgba(0, 0, 0, 0.2) 0px 5px 5px -3px, rgba(0, 0, 0, 0.14) 0px 8px 10px 1px, rgba(0, 0, 0, 0.12) 0px 3px 14px 2px',
      'bg-video': '0px 44px 50px -20px rgba(25, 92, 114, 0.16);',
    },
    background: {
      'video-overlay':
        'linear-gradient(0deg, rgba(0, 0, 0, 0.50) 0%, rgba(0, 0, 0, 0.00) 36.69%), linear-gradient(0deg, rgba(0, 0, 0, 0.20) 0%, rgba(0, 0, 0, 0.20) 100%) no-repeat',
    },
    backgroundColor: {
      'primary-blue': '#15A5E5',
    },
    borderRadius: {
      lg: 'var(--radius)',
      md: 'calc(var(--radius) - 2px)',
      sm: 'calc(var(--radius) - 4px)',
    },
    backgroundImage: {
      'landing-search': 'linear-gradient(180deg, #15a5e5 -24.11%, rgba(21, 165, 229, 0) 119.39%)',
      'landing-link':
        'linear-gradient(180deg, rgba(0, 0, 0, 0.00) 53.12%, rgba(0, 0, 0, 0.80) 107.66%)',
    },
    padding: {
      'safe-top': 'env(safe-area-inset-top)',
      'safe-bottom': 'env(safe-area-inset-bottom)',
    },
    height: {
      'screen-dynamic': '100dvh',
    },
  },
};
export const plugins = [
  require('tailwindcss-animate'),
  require('daisyui'),
  plugin(function ({ addUtilities }) {
    addUtilities({
      '.scrollbar-hide': {
        /* IE and Edge */
        '-ms-overflow-style': 'none',

        /* Firefox */
        'scrollbar-width': 'none',

        /* Safari and Chrome */
        '&::-webkit-scrollbar': {
          display: 'none',
        },
      },
    });
  }),
];

export const daisyui = {
  themes: ['light'],
};

export const corePlugins = {
  preflight: false,
};
