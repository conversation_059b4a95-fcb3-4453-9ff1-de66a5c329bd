import * as React from 'react';

import { cn } from '@/lib/utils';
import FormHelperText from '@/ui/atoms/FormHelperText';
import InputLabel from '@/ui/atoms/InputLabel';

import { twMerge } from 'tailwind-merge';

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<'textarea'> & {
    labelClassName?: string;
    label?: string;
    helperText?: string;
    required?: boolean;
    error?: boolean;
  }
>(({ className, labelClassName, helperText, error, label, required, ...props }, ref) => {
  return (
    <>
      {!!label && (
        <InputLabel className={twMerge('px-[14px]', labelClassName)} error={error}>
          {label}
          {required && <span className="required-mark">*</span>}
        </InputLabel>
      )}
      <textarea
        className={cn(
          'font-poppins flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
          error && 'border-error outline-error',
          className,
        )}
        ref={ref}
        {...props}
      />
      {helperText && (
        <div className="ml-2">
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </>
  );
});
Textarea.displayName = 'Textarea';

export { Textarea };
