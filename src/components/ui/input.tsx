import * as React from 'react';

import { cn } from '@/lib/utils';
import FormHelperText from '@/ui/atoms/FormHelperText';
import InputLabel from '@/ui/atoms/InputLabel';

import { twMerge } from 'tailwind-merge';

const Input = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<'input'> & {
    wrapperclassName?: string;
    labelClassName?: string;
    label?: string;
    helperText?: string;
    required?: boolean;
    error?: boolean;
    icon?: React.ReactNode;
  }
>(
  (
    {
      className,
      type,
      wrapperclassName,
      labelClassName,
      helperText,
      error,
      icon,
      label,
      required,
      ...props
    },
    ref,
  ) => {
    return (
      <>
        {!!label && (
          <InputLabel className={twMerge('px-[14px]', labelClassName)} error={error}>
            {label}
            {required && <span className="required-mark">*</span>}
          </InputLabel>
        )}
        <div className={twMerge('relative', wrapperclassName)}>
          {icon}
          <input
            type={type}
            className={cn(
              'font-poppins flex py-[8.5px] px-[14px] w-full rounded-sm border border-solid border-disabled bg-transparent text-base file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground ring-0 outline-carolina-blue focus-visible:ring-offset-0 focus-visible:ring-0 focus:ring-1 focus:ring-carolina-blue disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
              error && 'border-error outline-error',
              className,
            )}
            ref={ref}
            {...props}
          />
        </div>
        {helperText && (
          <div className="ml-2">
            <FormHelperText error={error}>{helperText}</FormHelperText>
          </div>
        )}
      </>
    );
  },
);
Input.displayName = 'Input';

export { Input };
