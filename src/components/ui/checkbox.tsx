import { twMerge } from 'tailwind-merge';

type Props = {
  className?: string;
} & React.HTMLProps<HTMLInputElement>;

const Checkbox = ({ className, ...rest }: Props) => {
  return (
    <input
      {...rest}
      type="checkbox"
      className={twMerge(
        'checkbox w-5 h-5 [--chkbg:#15A5E5] rounded border-solid border-carolina-blue shadow-sm checked:border-none cursor-pointer',
        className,
      )}
    />
  );
};

export default Checkbox;
