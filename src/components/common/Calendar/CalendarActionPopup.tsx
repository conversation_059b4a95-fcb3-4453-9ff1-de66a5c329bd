import { memo } from 'react';

import { Menu, MenuItem } from '@mui/material';

type Props = {
  anchorEl?: HTMLElement;
  open: boolean;
  handleClose: () => void;
  onEditRange: () => void;
  onDeleteRange: () => void;
};

const CalendarActionMenu = ({ anchorEl, open, handleClose, onDeleteRange, onEditRange }: Props) => {
  return (
    <Menu
      id="basic-menu"
      anchorEl={anchorEl}
      open={open}
      onClose={handleClose}
      MenuListProps={{
        'aria-labelledby': 'basic-button',
      }}
    >
      <MenuItem onClick={onEditRange}>Edit</MenuItem>
      <MenuItem onClick={onDeleteRange}>Delete</MenuItem>
    </Menu>
  );
};

export default memo(CalendarActionMenu);
