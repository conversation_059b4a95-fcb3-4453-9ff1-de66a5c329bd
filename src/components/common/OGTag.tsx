import Head from 'next/head';

type Props = {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: string;
};

const OGTag = ({ title = '', description = '', image = '', url = '', type = '' }: Props) => {
  return (
    <Head>
      {/* Facebook Meta Tags */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content="Nantucket Rentals" />
      <meta property="og:type" content={type} />

      {/* Twitter Meta Tags */}
      <meta property="twitter:domain" content="nantucketrentals.com" />
      <meta property="twitter:url" content={url} />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
    </Head>
  );
};

export default OGTag;
