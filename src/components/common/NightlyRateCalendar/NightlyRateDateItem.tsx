import React, { memo } from 'react';

import { AvailabilityCalendarData, RentalRatesCalendarData } from '@/redux/interfaces/properties';
import {
  formatAvailabilityDateString,
  formatDateItemRate,
  isBetweenTwoDates,
} from '@/utils/availabilityCalender';
import { AvailabilityType } from '@/views/PropertyListing/PropertyForms/AvailabilityCalendarForm';

import dayjs from 'dayjs';

import { Date, Weekday, Triangle, Rate } from '../Calendar/styles';

type Props = {
  dateObject: dayjs.Dayjs;
  date: number;
  rateRanges?: RentalRatesCalendarData;
  availableRanges: AvailabilityCalendarData[];
  active?: boolean;
  insideCurrentMonth: boolean;
};

const NightlyRateDateItem = ({
  date,
  active = false,
  availableRanges,
  rateRanges,
  dateObject,
  insideCurrentMonth,
}: Props) => {
  const bookedRanges = availableRanges.filter(
    ({ blockedFrom, blockedTo, blockedType }) =>
      isBetweenTwoDates(blockedFrom, blockedTo, dateObject) &&
      blockedType === AvailabilityType.LEASED,
  );
  const blockedRanges = availableRanges.filter(
    ({ blockedFrom, blockedTo, blockedType }) =>
      isBetweenTwoDates(blockedFrom, blockedTo, dateObject) &&
      blockedType !== AvailabilityType.LEASED,
  );

  const [
    [isBooked, bookedIsBeginning, bookedIsEnd],
    [isBlocked, blockedIsBeginning, blockedIsEnd],
  ] = [bookedRanges, blockedRanges].map((subRanges) => {
    const len = subRanges.length;
    const { blockedFrom, blockedTo } = subRanges[0] || {};
    const start = dateObject.isSame(formatAvailabilityDateString(blockedFrom));
    const end = dateObject.isSame(formatAvailabilityDateString(blockedTo));
    const isBeginning = !(len <= 1 && end);
    const isEnd = !(len <= 1 && start);
    return [len > 0, isBeginning, isEnd];
  });

  return (
    <Date>
      {insideCurrentMonth && (
        <>
          {isBlocked && blockedIsEnd && <Triangle $isInverted={blockedIsEnd} $isOwnerTime />}
          {isBooked && bookedIsEnd && <Triangle $isInverted={bookedIsEnd} $isLeased />}
          {isBlocked && blockedIsBeginning && <Triangle $isOwnerTime />}
          {isBooked && bookedIsBeginning && <Triangle $isLeased />}
        </>
      )}

      {insideCurrentMonth && (
        <>
          <Weekday $active={active || isBooked || isBlocked}>{date}</Weekday>
          <Rate $active={active || isBooked || isBlocked}>
            {rateRanges?.propertyWeeklyRentInteger
              ? formatDateItemRate(rateRanges.propertyWeeklyRentInteger)
              : 'NA'}
          </Rate>
        </>
      )}
    </Date>
  );
};

export default memo(NightlyRateDateItem);
