import React, { useMemo, memo } from 'react';

import { EntityID } from '@/redux/interfaces';
import { AvailabilityCalendarData, RentalRatesCalendarData } from '@/redux/interfaces/properties';
import {
  getAvailabilityDataForCurrentMonth,
  getFirstDayOfMonth,
  getNumberOfDaysInMonth,
  getRentalRatesForCurrentMonth,
} from '@/utils/availabilityCalender';
import { Box } from '@mui/material';

import dayjs from 'dayjs';

import {
  CalendarWrapper,
  Weekday,
  WeekdaysWrapper,
  WeekDayWrapper,
  Month,
} from '../Calendar/styles';

import NightlyRateDateItem from './NightlyRateDateItem';

const MONTHS = dayjs.months();
const DAY_NAMES = dayjs.weekdaysShort();

type Props = {
  month: number;
  year: number;
  rateRanges?: RentalRatesCalendarData[];
  availableRanges: AvailabilityCalendarData[];
  onEdit?: (id: EntityID) => void;
  onDelete?: (id: EntityID) => void;
  hideHeader?: boolean;
};

const Calendar = ({ month, year, availableRanges = [], rateRanges, hideHeader }: Props) => {
  const numberOfDays = useMemo(() => getNumberOfDaysInMonth(year, month), [year, month]);
  const firstDayOfMonth = useMemo(() => getFirstDayOfMonth(year, month), [year, month]);
  const numberOfDaysLastMonth = useMemo(
    () =>
      month > 0 ? getNumberOfDaysInMonth(year, month - 1) : getNumberOfDaysInMonth(year - 1, 11),
    [year, month],
  );
  const availableRangesForCurrentMonth = useMemo(
    () => getAvailabilityDataForCurrentMonth(availableRanges, month + 1, year),
    [availableRanges, month, year],
  );
  const rentalRatesForCurrentMonth = useMemo(
    () => getRentalRatesForCurrentMonth(rateRanges ?? [], month + 1, year),
    [month, rateRanges, year],
  );

  const totalSlots: JSX.Element[] = [];

  for (let i = numberOfDaysLastMonth - firstDayOfMonth; i < numberOfDaysLastMonth; i++) {
    totalSlots.push(
      <NightlyRateDateItem
        key={`prev${i}`}
        rateRanges={rentalRatesForCurrentMonth.find((_r) =>
          dayjs(_r.rateStartFrom).isSame(
            dayjs(`${year}/${month + 1}/${i + 1}`, 'YYYY/M/D').format('YYYY-MM-DD'),
          ),
        )}
        availableRanges={availableRangesForCurrentMonth}
        dateObject={dayjs(
          month > 0 ? `${year}/${month}/${i + 1}` : `${year - 1}/${12}/${i + 1}`,
          'YYYY/M/D',
        )}
        date={i + 1}
        insideCurrentMonth={false}
      />,
    );
  }

  for (let i = 0; i < numberOfDays; i++) {
    totalSlots.push(
      <NightlyRateDateItem
        key={`day${i}`}
        // rateRanges={rentalRatesForCurrentMonth}
        availableRanges={availableRangesForCurrentMonth}
        dateObject={dayjs(`${year}/${month + 1}/${i + 1}`, 'YYYY/M/D')}
        date={i + 1}
        rateRanges={rentalRatesForCurrentMonth.find((_r) =>
          dayjs(_r.rateStartFrom).isSame(
            dayjs(`${year}/${month + 1}/${i + 1}`, 'YYYY/M/D').format('YYYY-MM-DD'),
          ),
        )}
        active
        insideCurrentMonth={true}
      />,
    );
  }

  for (let i = 0; i < (42 - firstDayOfMonth - numberOfDays) % 7; i++) {
    totalSlots.push(
      <NightlyRateDateItem
        key={`next${i}`}
        rateRanges={rentalRatesForCurrentMonth.find((_r) =>
          dayjs(_r.rateStartFrom).isSame(
            dayjs(`${year}/${month + 1}/${i + 1}`, 'YYYY/M/D').format('YYYY-MM-DD'),
          ),
        )}
        availableRanges={availableRangesForCurrentMonth}
        dateObject={dayjs(`${year}/${month + 1}/${i + 1}`, 'YYYY/M/D').add(1, 'month')}
        date={i + 1}
        insideCurrentMonth={false}
      />,
    );
  }

  const rows: JSX.Element[][] = [];

  const weeks = Math.ceil(totalSlots.length / 7);
  for (let i = 0; i < weeks; i++) {
    const cells: JSX.Element[] = [];
    for (let j = 0; j < 7; j++) {
      const day = i * 7 + j;
      const row = totalSlots[day];
      cells.push(row || <td key={`blank${day}`}>{''}</td>);
    }
    let day = i * 7 - firstDayOfMonth;
    if (day < 0) {
      day = 0;
    }
    rows.push(cells);
  }

  return (
    <Box className="Calendar">
      {!hideHeader && (
        <Month>
          {MONTHS[month]} {year}
        </Month>
      )}

      <CalendarWrapper>
        <WeekdaysWrapper>
          <WeekDayWrapper>
            {DAY_NAMES.map((day) => (
              <th key={day} style={{ width: '12.5%' }}>
                <Weekday $active>{day}</Weekday>
              </th>
            ))}
          </WeekDayWrapper>
        </WeekdaysWrapper>
        <tbody>
          {rows.map((d, i) => (
            <WeekDayWrapper key={i}>{d}</WeekDayWrapper>
          ))}
        </tbody>
      </CalendarWrapper>
    </Box>
  );
};

export default memo(Calendar);
