import React, { useEffect } from 'react';

import DownTriangle from '@/common/assets/svgs/DownTriangle';
import { P1 } from '@/components/core/Typography';
import FormHelperText from '@/ui/atoms/FormHelperText';
import { getGuestValuesSchema } from '@/views/Checkout/helpers';

import dynamic from 'next/dynamic';

import { FilterItem } from './styles';

const GuestorSelectorDropdownComponent = dynamic(
  () => import('./GuestorSelectorDropdownComponent'),
);

export type GuestsValues = {
  adults: number;
  children: number;
};

type Props = {
  hideLabel?: boolean;
  capacity?: number;
  paddingY?: string;
  guestsValues: GuestsValues;
  setGuestsValues: (guests: GuestsValues) => void;
  setGuestValuesValid?: (value: boolean) => void;
  text?: string | React.ReactNode;
};

const GuestsSelector = ({
  hideLabel,
  capacity,
  guestsValues,
  setGuestsValues,
  setGuestValuesValid,
  paddingY,
  text,
}: Props) => {
  const [selected, setSelected] = React.useState<any | null>(null);
  const [menuAnchorEl, setMenuAnchorEl] = React.useState<null | HTMLElement>(null);
  const [guestValidationError, setguestValidationError] = React.useState<any>(null);
  const open = Boolean(menuAnchorEl);

  const handleGuestClick = (event?: React.MouseEvent<HTMLElement>) => {
    if (event) {
      setMenuAnchorEl(event.currentTarget);
    }
    setSelected(true);
  };

  const handleClose = () => {
    setMenuAnchorEl(null);
    setSelected(null);
  };

  const handleIncrement = (field: keyof GuestsValues) => {
    if ((capacity && guestsValues[field] < capacity) || !capacity) {
      setGuestsValues({
        ...guestsValues,
        [field]: guestsValues[field] + 1,
      });
    }
  };

  const handleDecrement = (field: keyof GuestsValues) => {
    if (guestsValues[field] > 0) {
      setGuestsValues({
        ...guestsValues,
        [field]: guestsValues[field] - 1,
      });
    }
  };

  useEffect(() => {
    const validateGuestValues = async () => {
      const schema = getGuestValuesSchema(capacity as number);
      try {
        await schema.validate(guestsValues);
        setGuestValuesValid?.(true);
        setguestValidationError(null);
      } catch (error) {
        setguestValidationError(`Maximum guests for this property is ${capacity}`);
        setGuestValuesValid?.(false);
      }
    };

    if (capacity) {
      validateGuestValues();
    }
  }, [capacity, guestsValues, setGuestValuesValid]);

  useEffect(() => {
    if (guestValidationError) {
      setGuestValuesValid?.(false);
    }
  }, [guestValidationError, setGuestValuesValid]);

  return (
    <div className="relative w-full">
      <FilterItem
        paddingY={paddingY ? `${paddingY} !important` : '10px !important'}
        className="filter-item"
        onClick={handleGuestClick}
        $isSelected={selected}
      >
        {text ? (
          text
        ) : (
          <div>
            {!hideLabel && (
              <P1 color={'text.disabled'} fontWeight={500}>
                Guests
              </P1>
            )}

            <P1 color={'text.disabled'} fontSize={'12px'}>
              {(guestsValues?.adults ?? 1) + (guestsValues?.children ?? 0)} guests
            </P1>
          </div>
        )}

        <DownTriangle className={`w-6 h-6 text-[#6D7381] ${selected && 'rotate-180'}`} />
      </FilterItem>
      {open && (
        <GuestorSelectorDropdownComponent
          menuAnchorEl={menuAnchorEl}
          handleClose={handleClose}
          open={open}
          handleIncrement={handleIncrement}
          handleDecrement={handleDecrement}
          guestsValues={guestsValues}
          capacity={capacity}
          guestValidationError={guestValidationError}
        />
      )}
      {guestValidationError && (
        <FormHelperText className="pl-2.5" error>
          {guestValidationError as string}
        </FormHelperText>
      )}
    </div>
  );
};

export default GuestsSelector;
