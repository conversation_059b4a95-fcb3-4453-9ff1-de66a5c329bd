import React from 'react';

import { MinusIcon, PlusIcon } from '@heroicons/react/20/solid';

import { P1 } from '@/components/core/Typography';
import FormHelperText from '@/ui/atoms/FormHelperText';
import { AddRemoveContainer, ControlledSelection, IconContainer } from '@/views/Checkout/styles';
import { Box, Menu, PaperProps } from '@mui/material';

import { GuestsValues } from '.';

const PaperStyle: PaperProps = {
  elevation: 0,
  sx: {
    overflow: 'visible',
    filter: 'drop-shadow(0px 1px 1px rgba(0,0,0,0.1))',
    border: '1px solid #DEE1EB',
    borderRadius: '4px',
    minWidth: '96px',
    width: '100%',
    maxWidth: '340px',
    overflowY: 'scroll',
    marginTop: '4px',
    '.MuiList-root': {
      padding: '12px 0px 6px 12px',
    },
  },
};

type Props = {
  menuAnchorEl: HTMLElement | null;
  handleClose: () => void;
  open: boolean;
  handleIncrement: (type: keyof GuestsValues) => void;
  handleDecrement: (type: keyof GuestsValues) => void;
  guestsValues: GuestsValues;
  capacity?: number;
  guestValidationError?: any;
};

const GuestorSelectorDropdownComponent = ({
  menuAnchorEl,
  handleClose,
  open,
  handleIncrement,
  handleDecrement,
  guestsValues,
  capacity,
  guestValidationError,
}: Props) => {
  return (
    <Menu anchorEl={menuAnchorEl} open={open} onClose={handleClose} PaperProps={PaperStyle}>
      <ControlledSelection>
        <P1 fontWeight={500} fontSize={'16px'}>
          Adults
        </P1>

        <AddRemoveContainer>
          <IconContainer
            onClick={() => handleDecrement('adults')}
            disabled={guestsValues.adults === 1}
          >
            <MinusIcon className="w-4 h-4" />
          </IconContainer>
          <P1 fontSize="16px" fontWeight={500}>
            {guestsValues.adults}
          </P1>
          <IconContainer
            onClick={() => handleIncrement('adults')}
            disabled={guestsValues.adults === capacity}
          >
            <PlusIcon className="w-4 h-4" />
          </IconContainer>
        </AddRemoveContainer>
      </ControlledSelection>

      <ControlledSelection>
        <P1 fontWeight={500} fontSize={'16px'}>
          Children
        </P1>
        <AddRemoveContainer>
          <IconContainer
            onClick={() => handleDecrement('children')}
            disabled={guestsValues.children === 0}
          >
            <MinusIcon className="w-4 h-4" />
          </IconContainer>
          <P1 fontSize="16px" fontWeight={500}>
            {guestsValues.children}
          </P1>
          <IconContainer
            onClick={() => handleIncrement('children')}
            disabled={guestsValues.children === capacity}
          >
            <PlusIcon className="w-4 h-4" />
          </IconContainer>
        </AddRemoveContainer>
      </ControlledSelection>
      {guestValidationError && (
        <FormHelperText className="pl-2.5" error>
          {guestValidationError as string}
        </FormHelperText>
      )}

      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          padding: '10px 6px',
          cursor: 'pointer',
          fontWeight: '500',
          textDecoration: 'underline',
        }}
        onClick={handleClose}
      >
        Close
      </Box>
    </Menu>
  );
};

export default GuestorSelectorDropdownComponent;
