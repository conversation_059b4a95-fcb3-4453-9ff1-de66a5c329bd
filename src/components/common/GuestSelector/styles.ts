import { colors } from '@/common/style/theme';
import { H2 } from '@/components/core/Typography';
import { Box, Button } from '@mui/material';

import styled, { css } from 'styled-components';

export const Heading = styled(H2)`
  color: ${colors.primary.main};
`;

export const FilterItem = styled(Box)<{
  $isSelected?: boolean;
}>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 6px 8px 12px;
  background: ${colors.white};
  border: 1px solid ${colors.grey.border};
  box-sizing: border-box;
  border-radius: 4px;
  flex: 1;
  cursor: pointer;

  &:hover {
    border: 1px solid ${colors.black};
  }

  ${({ $isSelected }) =>
    $isSelected &&
    css`
      border: 2px solid ${colors.blue.main};
      color: ${colors.white};
    `}
`;

export const FilterButton = styled(Button)`
  font-weight: 400;
  font-size: 14px;
  line-height: 19px;
  display: flex;
  justify-content: center;
  width: 180px;
  max-width: 180px;
  height: 44px;
  text-transform: capitalize;

  img {
    margin-left: 10px;
  }

  @media (max-width: 900px) {
    width: 165px;
    max-width: 165px;
  }
`;
