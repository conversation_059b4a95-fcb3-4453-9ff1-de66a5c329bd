'use client';

import { useEffect } from 'react';

import { HOME_ROUTE } from '@/constants/routes';

import Image from 'next/image';
import Link from 'next/link';

export default function Error({ error, reset }: { error: Error; reset: () => void }) {
  useEffect(() => {
    console.error(error);
  }, [error]);

  return (
    <>
      <div className="relative">
        <div className="h-[390px] sm:h-[410px] md:h-[520px] lg:h-[600px]">
          <div className="w-screen h-full bg-landing-search">
            <Image
              alt="landing bg"
              className="w-full h-full object-cover object-top mix-blend-multiply"
              src="/images/bgTop.webp"
              loading="eager"
              sizes="60vw"
              priority
              fill
            />
          </div>
        </div>
        <div className=" text-white text-center absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
          <h2 className="text-2xl font-bold mb-4">Oops! Something went wrong.</h2>
          <p>We're sorry, but we couldn't load this page.</p>
          <Link
            href={HOME_ROUTE}
            className="flex items-center justify-center m-auto bg-carolina-blue px-4 py-2 rounded-sm text-white no-underline w-[280px]"
          >
            Go back to the homepage
          </Link>
          <button onClick={reset} className="bg-primary text-white px-4 py-2 rounded">
            Try Again
          </button>
        </div>
      </div>
    </>
  );
}
