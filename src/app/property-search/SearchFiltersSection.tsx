import { UserIcon } from '@heroicons/react/24/outline';

import SearchFilterButton from '@/clients/views/property-search/SearchFilterButton';
import { GuestsValues } from '@/components/common/GuestSelector';
import { getDateRangeStringMobileHeader } from '@/utils/listing-search';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  defaultAreas?: string[];
  bedrooms?: number;
  defaultRange?: [number, number];
  defaultAmenities?: string[];
  defaultGuestValues?: GuestsValues;
  filterCount: number;
  date_min?: string;
  date_max?: string;
  className?: string;
  isMapView?: boolean;
};

const SearchFiltersSection = ({
  defaultAreas,
  bedrooms,
  defaultRange,
  defaultAmenities,
  defaultGuestValues,
  filterCount,
  date_min,
  date_max,
  className,
  isMapView,
}: Props) => {
  return (
    <div
      className={twMerge(
        'absolute top-4 right-4 lg:[position:initial] rounded-sm border border-solid border-disabled flex items-center px-3 py-0 lg:py-2 text-sm text-primary-text lg:min-w-[180px] min-h-[48px]',
        className,
      )}
    >
      <div className="max-w-[65px] flex items-center gap-x-1 mr-2 lg:hidden">
        {getDateRangeStringMobileHeader(date_min, date_max)}
      </div>
      <div className="w-1 h-1 rounded-full bg-primary-text lg:hidden" />
      <div className="flex items-center mx-2 lg:hidden">
        {(defaultGuestValues?.adults ?? 1) + (defaultGuestValues?.children ?? 0)}
        <UserIcon className="w-4 h-4 ml-1" />
      </div>
      {!isMapView && <div className="w-1 h-1 rounded-full bg-primary-text lg:hidden" />}
      <SearchFilterButton
        defaultAreas={defaultAreas}
        defaultAmenities={defaultAmenities}
        bedrooms={bedrooms}
        defaultRange={defaultRange}
        defaultGuestValues={defaultGuestValues}
        filterCount={filterCount}
        hideLabel={isMapView}
        className={classNames({
          'ml-0': isMapView,
        })}
      />
    </div>
  );
};

export default SearchFiltersSection;
