import LoginPageClientWrapper from '@/clients/views/login/LoginPageClientWrapper';

import Image from 'next/image';

import Header from '../components/common/Header';

export default function LoginPage() {
  return (
    <LoginPageClientWrapper>
      <Header />
      <div className="relative">
        <div className="h-[390px] sm:h-[410px] md:h-[520px] lg:h-[600px]">
          <div className="w-screen h-full bg-landing-search">
            <Image
              alt="landing bg"
              className="w-full h-full object-cover object-top mix-blend-multiply"
              src="/images/bgTop.webp"
              loading="eager"
              sizes="60vw"
              priority
              fill
            />
          </div>
        </div>
      </div>
    </LoginPageClientWrapper>
  );
}
