import Link from 'next/link';
import { twMerge } from 'tailwind-merge';

type Props = {
  className?: string;
  linkItemClassName?: string;
};

const HeaderLinks = ({ className, linkItemClassName }: Props) => {
  return (
    <div
      className={twMerge(
        'flex flex-row items-center gap-4 md:gap-2 rounded-[30px] bg-[rgba(255,255,255,0.05)] border border-solid border-[#E7E7E9] p-1',
        className,
      )}
    >
      <Link
        href="https://nantucketrentals.com/the-island/"
        className={twMerge(
          'p-2 text-metal-gray no-underline hover:bg-[#BCCBE0] hover:rounded-[30px] hover:text-white',
          linkItemClassName,
        )}
      >
        The Island
      </Link>
      <Link
        href="https://nantucketrentals.com/things-to-do/"
        className={twMerge(
          'p-2 text-metal-gray no-underline hover:bg-[#BCCBE0] hover:rounded-[30px] hover:text-white',
          linkItemClassName,
        )}
      >
        Things To Do
      </Link>
      <Link
        href="https://nantucketrentals.com/where-to-go/"
        className={twMerge(
          'p-2 text-metal-gray no-underline hover:bg-[#BCCBE0] hover:rounded-[30px] hover:text-white',
          linkItemClassName,
        )}
      >
        Where To Go
      </Link>
      <Link
        href="https://nantucketrentals.com/our-picks/"
        className={twMerge(
          'p-2 text-metal-gray no-underline hover:bg-[#BCCBE0] hover:rounded-[30px] hover:text-white',
          linkItemClassName,
        )}
      >
        Our Picks
      </Link>
      <Link
        href="/list-your-home"
        className={twMerge(
          'p-2 text-metal-gray no-underline hover:bg-[#BCCBE0] hover:rounded-[30px] hover:text-white',
          linkItemClassName,
        )}
      >
        Owners
      </Link>
    </div>
  );
};

export default HeaderLinks;
