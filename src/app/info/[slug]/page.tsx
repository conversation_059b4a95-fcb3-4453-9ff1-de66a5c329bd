import parse from 'html-react-parser';

import Header from '@/app/components/common/Header';
import { getAdminPageBySlug } from '@/services/admin';
import { Nullable, WYSIWYGPage } from '@/types/common';

import { Metadata } from 'next';
import { notFound } from 'next/navigation';

type PageProps = {
  params: Promise<{ slug: string | string[] }>;
};

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;

  const page = await getAdminPageBySlug<Nullable<WYSIWYGPage>>(slug as string);

  return {
    title: page?.titleTag ?? '',
    description: page?.metaDescription ?? '',
    metadataBase: new URL('https://nantucketrentals.com/'),
    alternates: {
      canonical: page?.metaCanonical ?? '',
    },
    robots: page?.metaRobots ?? '',
  };
}

export default async function InfoPageDetails({ params }: PageProps) {
  const { slug } = await params;
  try {
    const page = await getAdminPageBySlug<Nullable<WYSIWYGPage>>(slug as string);
    if (!page) {
      return notFound();
    }
    return (
      <>
        <Header />
        <main>
          <div className="container py-10 my-0 mx-auto">{parse(page?.description ?? '')}</div>
        </main>
      </>
    );
  } catch (error) {
    console.error(error);
    throw new Error('Something went wrong while loading the page.');
  }
}

export const revalidate = 3600;
