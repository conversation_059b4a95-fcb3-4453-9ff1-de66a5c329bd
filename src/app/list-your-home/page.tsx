import { H1, H2, P } from '@/app/ui/common/Typography';
import ListYourHomeHeader from '@/app/views/list-your-home/ListYourHomeHeader';
import HoverPlayVideo from '@/clients/components/common/HoverPlayVideo';
import TypeFormWidget from '@/clients/views/list-your-home/TypeFormWidget';

import Image from 'next/image';
import Link from 'next/link';

import './style.css';

export default async function ListYourHome() {
  return (
    <>
      <ListYourHomeHeader>
        <div className="flex items-center justify-center absolute inset-0 top-[75%] left-1/2 -translate-x-1/2 -translate-y-[75%]">
          <Link
            href="#listing-form"
            scroll={true}
            className="text-center rounded-[48px] border border-solid border-white px-[48px] py-4 btn bg-carolina-blue hover:bg-[#0277BD] text-white"
          >
            List Your Vacation Home
          </Link>
        </div>
      </ListYourHomeHeader>
      <main>
        <div className="container py-[60px] md:py-[80px] lg:py-[100px]">
          <H1 className="text-6 md:text-8 lg:text-[40px] font-medium text-center m-0 md:px-[15vw] text-carolina-blue">
            The Nantucket Rentals <em>Advantage</em>
          </H1>
          <iframe
            className="w-full h-[220px] md:h-[520px] rounded-lg my-10 md:hidden"
            src="https://www.youtube.com/embed/Hgw7Bd3pXGE?si=O2eFVZjaEbPOpTc5"
            title="YouTube video player"
            frameBorder="0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            referrerPolicy="strict-origin-when-cross-origin"
            allowFullScreen
          />
          <p className="text-center px-4 md:px-[100px] text-grey-main mb-8">
            Managing your Nantucket rental shouldn’t feel like a full-time job. In today’s world,
            homeowners list with multiple brokerages, and every time a rate or availability change
            happens, they’re expected to update each one manually—over and over again. That’s
            inefficient, time-consuming, and, let’s be honest, just plain frustrating. Nantucket
            Rentals makes managing your rental effortless by giving you one central place to control
            your listing and automatically share updates with any brokerage on Nantucket. Organize
            your bookings, set rates and rules with ease, and ensure your rental is always
            represented accurately—without the hassle. Plus, our team is here to handle guest
            check-ins, property oversight, and offer expert advice on pricing and booking strategies
            to maximize your earnings with less stress.
          </p>
          <div className="flex md:grid md:grid-cols-3 gap-x-6 overflow-x-scroll scrollbar-hide w-full">
            <div className="min-w-full md:w-full h-[420px] rounded-[12px] overflow-hidden relative">
              <Image
                src="/images/landing/bottom-section-thumb-1.png"
                alt="Bottom section thumb 1"
                width={0}
                height={0}
                sizes="100vw"
                priority
                className="absolute top-0 left-0 w-full h-full object-cover z-0"
              />
              <HoverPlayVideo
                className="absolute top-0 left-0 w-full h-full object-cover z-0"
                src="/videos/NR-Exp-Vid1.mp4"
                loop
                muted
                playsInline
              >
                <div className="absolute bottom-20 px-10 text-white z-[1]">
                  <p className="uppercase text-xl font-medium">One Platform, Full Control</p>
                  <p className="italic m-0">
                    Update once, share everywhere.Manage your rates, availability, and rental
                    details in one place and seamlessly share updates with any brokerage on
                    Nantucket—no more tedious manual updates.
                  </p>
                </div>
              </HoverPlayVideo>
            </div>
            <div className="min-w-full md:w-full h-[420px] rounded-[12px] overflow-hidden relative">
              <Image
                src="/images/landing/bottom-section-thumb-2.png"
                alt="Bottom section thumb 2"
                width={0}
                height={0}
                sizes="100vw"
                priority
                className="absolute top-0 left-0 w-full h-full object-cover z-0"
              />
              <HoverPlayVideo
                className="absolute top-0 left-0 w-full h-full object-cover z-0"
                src="/videos/NR-Exp-Vid2.mp4"
                loop
                muted
                playsInline
              >
                <div className="absolute bottom-20 px-10 text-white z-[1]">
                  <p className="uppercase text-xl font-medium">Organized & Efficient</p>
                  <p className="italic m-0">
                    Ditch the spreadsheets and email chaos.Upload bookings from brokerages, Airbnb,
                    or VRBO directly into your calendar, store lease agreements for easy access, and
                    keep everything organized in one streamlined dashboard.
                  </p>
                </div>
              </HoverPlayVideo>
            </div>
            <div className="min-w-full md:w-full h-[420px] rounded-[12px] overflow-hidden relative">
              <Image
                src="/images/landing/bottom-section-thumb-3.png"
                alt="Bottom section thumb 3"
                width={0}
                height={0}
                sizes="100vw"
                priority
                className="absolute top-0 left-0 w-full h-full object-cover z-0"
              />
              <HoverPlayVideo
                className="absolute top-0 left-0 w-full h-full object-cover z-0"
                src="/videos/NR-Exp-Vid3.mp4"
                loop
                muted
                playsInline
              >
                <div className="absolute bottom-20 px-10 text-white z-[1]">
                  <p className="uppercase text-xl font-medium">Maximize Your Rental Potential</p>
                  <p className="italic m-0">
                    More exposure, smarter bookings.Set your own pricing, minimum stays, and
                    seasonal availability—including overlooked months like spring and fall. Keep
                    your listing up to date with accurate details, high-quality photos, and
                    optimized booking rules.
                  </p>
                </div>
              </HoverPlayVideo>
            </div>
          </div>
        </div>
        <div className="p-[6px] md:p-2">
          <div className="relative h-[800px] md:h-[600px] shadow-bg-video">
            <Image
              src="/images/list-your-home-bottom.jpg"
              alt="Bottom Background Image"
              width={0}
              height={0}
              sizes="100vw"
              className="w-full h-full z-[-1] rounded-[30px] bg-contain bg-no-repeat object-cover"
            />
            <div className="absolute inset-0 video-bg-overlay !bg-[rgba(0,0,0,0.35)] rounded-[30px]" />

            <div className="text-center absolute top-1/2 -translate-y-2/4 z-[1] left-0 right-0">
              <p className="uppercase text-white tracking-[1.44px] text-xs md:text-base leading-[140%] font-bold m-0">
                Nantucket rentals listing manager
              </p>
              <H1 className="text-white px-5 md:px-[15%] my-6 text-[32px] md:text-[50px] font-medium leading-[120%]">
                Easily manage your vacation rental, and share updates with selected brokerages.
              </H1>
              <Link
                href={'/listing-manager'}
                className="rounded-[30px] cursor-pointer font-semibold px-8 w-max h-[48px] border-white border-solid border-[0.25px] mx-auto no-underline bg-carolina-blue text-white flex items-center justify-center"
              >
                Homeowner Login
              </Link>
            </div>
          </div>
        </div>
        <section id="listing-form" className="container max-w-[860px] my-[100px]">
          <H2 className="mb-4">List Your Home with Nantucket Rentals</H2>
          <P className="text-metal-gray">
            If you're tired of the tedious task of responding to emails and making repetitive phone
            calls to multiple brokerages, look no further! Nantucket Rentals offers a one-stop
            platform for making all of these changes in one place and choosing which brokerages you
            want to share these updates with.
          </P>

          <div className="my-10">
            <TypeFormWidget />
          </div>
        </section>
      </main>
    </>
  );
}
