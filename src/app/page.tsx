import { Suspense } from 'react';

import { ArrowRightIcon } from '@heroicons/react/24/outline';

import StructuredData from '@/app/components/common/StructuredData';
import LandingSearchWrapper from '@/clients/views/landing/LandingSearchWrapper';
import { PROPERTY_SEARCH_ROUTE } from '@/constants/routes';
import { getLandingPageContent } from '@/services/server/landingPage';
import { LandingContent, SECTION_NAME } from '@/types/landing';
import { generateHomepageJsonLd } from '@/utils/structuredData';

import { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';

import { H1 } from './ui/common/Typography';
import LandingHeader from './views/landing/LandingHeader';
import LandingPageLinks from './views/landing/LandingPageLinks';
import LandingPageListingTabs from './views/landing/LandingPageListingsTab';
import LandingPopularSearches from './views/landing/LandingPopularSearches';
import NantucketExperience from './views/landing/NantucketExperience';

export const metadata: Metadata = {
  title: 'Nantucket Rentals | Leading Source for 2025 Rentals',
  description:
    'Nantucket rentals houses and cottages offered direct from homeowners and Nantucket`s most trusted local brokerages. Search, book and pay for your rental online.',
  metadataBase: new URL('https://nantucketrentals.com/'),
  alternates: {
    canonical: 'https://nantucketrentals.com/',
  },
  robots: 'index,follow',
  openGraph: {
    title: 'Nantucket Rentals | Leading Source for 2025 Rentals',
    description:
      'Nantucket rentals houses and cottages offered direct from homeowners and Nantucket`s most trusted local brokerages. Search, book and pay for your rental online.',
    type: 'website',
    siteName: 'Nantucket Rentals',
    url: 'https://nantucketrentals.com',
  },
  other: {
    'facebook-domain-verification': 'vs7bc1k4hi7tzajtgntzap2qa05vev',
  },
};

export default async function Landing() {
  const landingData = await getLandingPageContent<LandingContent[]>();
  const section1 = landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_1);

  return (
    <>
      <StructuredData data={generateHomepageJsonLd()} />
      <LandingHeader>
        <div className="text-center text-white font-medium mt-[150px] text-[40px] md:text-[64px] lg:text-[72px] px-[10vw]">
          {section1?.heading}
        </div>
        <p className="text-center text-white my-4">{section1?.subheading}</p>
        <div className="w-full flex">
          <Link
            href={PROPERTY_SEARCH_ROUTE}
            className="rounded-[30px] cursor-pointer font-semibold px-8 h-[48px] border-white border-solid border-[0.25px] mx-auto no-underline bg-carolina-blue text-white flex items-center justify-center"
          >
            Search Vacation Rentals
          </Link>
        </div>
      </LandingHeader>
      <main>
        <div className="container">
          <div className="py-[60px] md:py-[80px] lg:py-[100px]">
            <p className="uppercase text-[#00B2FF] tracking-[1.92px] text-xs md:text-base leading-[140%] font-medium m-0">
              {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_2)?.subheading ??
                ''}
            </p>
            <div className="flex items-center justify-between my-8">
              <H1 className="text-6 md:text-8 lg:text-[40px] font-medium m-0">
                {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_2)?.heading ??
                  ''}
              </H1>
              <Link
                prefetch={true}
                href={PROPERTY_SEARCH_ROUTE}
                className="no-underline hidden md:block"
              >
                <div className="border-solid border-[#15A5E5] text-[#15A5E5] px-[26px] py-4 flex gap-2.5 items-center rounded-full">
                  Explore more
                  <ArrowRightIcon className="w-4 h-4" />
                </div>
              </Link>
            </div>
            <Suspense fallback="Loading...">
              <LandingPopularSearches />
              <Link prefetch={true} href={PROPERTY_SEARCH_ROUTE} className="no-underline md:hidden">
                <div className="border-solid border-[#15A5E5] text-[#15A5E5] px-[26px] py-4 flex gap-2.5 items-center rounded-full w-max mt-10">
                  Explore more
                  <ArrowRightIcon className="w-4 h-4" />
                </div>
              </Link>
            </Suspense>
          </div>
          <div className="py-[60px] md:py-[80px] lg:py-[100px]">
            <p className="uppercase text-[#00B2FF] tracking-[1.92px] text-xs md:text-base leading-[140%] font-medium m-0 md:text-center">
              {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_3)?.subheading ??
                ''}
            </p>
            <H1 className="text-6 md:text-8 lg:text-[40px] font-medium md:text-center m-0 md:px-[15vw] leading-[120%] mt-6 md:mt-8">
              {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_3)?.heading ?? ''}
            </H1>
            <div className="mt-10 md:mt-[60px]">
              <div className="flex items-center justify-center">
                <LandingSearchWrapper />
              </div>
              <Suspense fallback="Loading...">
                <LandingPageListingTabs />
              </Suspense>
            </div>
          </div>
          <div className="py-[60px] md:py-[80px] lg:py-[100px]">
            <div className="flex md:items-start flex-col lg:flex-row justify-between">
              <H1 className="text-6 md:text-8 lg:text-[40px] font-medium m-0 leading-[110%]">
                {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_4)?.heading ??
                  ''}
              </H1>
              <p className="text-sm md:text-base lg:text-lg text-[#6D83A3] md:line-clamp-2 max-w-sm md:my-2 ">
                {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_4)
                  ?.subheading ?? ''}
              </p>
            </div>
            <Suspense fallback="Loading...">
              <LandingPageLinks />
            </Suspense>
          </div>
          <div className="py-[60px] md:py-[80px] lg:py-[100px]">
            <H1 className="text-6 md:text-8 lg:text-[40px] font-medium text-center m-0 md:px-[15vw] text-carolina-blue">
              The Nantucket Rentals <em>experience</em>
            </H1>
            <p className="text-center px-4 md:px-[100px] text-grey-main mb-8">
              Discover the most comprehensive vacation rental platform on Nantucket. The Nantucket
              Rentals Experience brings together the largest selection of properties, seamlessly
              combining direct homeowner listings with those from trusted local brokerages. Designed
              with your convenience in mind, our platform allows you to book online with ease, skip
              unnecessary delays, and secure your dream rental. From property preparation to insider
              recommendations, we ensure your stay is nothing short of extraordinary.
            </p>
            <NantucketExperience />
          </div>
        </div>
        <div className="relative p-[6px] md:p-2 h-[800px] md:h-[600px] pt-[60px] md:pt-[80px] lg:pt-[100px]">
          <Image
            src={
              landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_6)
                ?.background_image_mobile ?? 'https://fakeimg.pl/600x400/DDDDDD/DDDDDD'
            }
            alt="Bottom Background Image"
            width={0}
            height={0}
            sizes="100vw"
            className="w-full h-full z-[-1] rounded-[30px] bg-cover bg-no-repeat md:hidden"
          />
          <Image
            src={
              landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_6)
                ?.background_image_ipad ?? 'https://fakeimg.pl/600x400/DDDDDD/DDDDDD'
            }
            alt="Bottom Background Image"
            width={0}
            height={0}
            sizes="100vw"
            priority
            className="w-full h-full z-[-1] rounded-[30px] bg-cover bg-no-repeat hidden md:block lg:hidden"
          />
          <Image
            src={
              landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_6)
                ?.background_image_desktop ?? 'https://fakeimg.pl/600x400/DDDDDD/DDDDDD'
            }
            alt="Bottom Background Image"
            width={0}
            height={0}
            sizes="100vw"
            priority
            className="w-full h-full z-[-1] rounded-[30px] bg-cover bg-no-repeat hidden lg:block"
          />

          <div className="text-center absolute top-1/2 -translate-y-2/4 z-[1] left-0 right-0">
            <p className="uppercase text-white tracking-[1.44px] text-xs md:text-base leading-[140%] font-medium m-0">
              {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_6)?.subheading ??
                ''}
            </p>
            <H1 className="text-white px-5 md:px-[15%] my-6 text-[32px] md:text-[50px] font-medium leading-[120%]">
              {landingData?.find((_s) => _s.section_name === SECTION_NAME.SECTION_6)?.heading ?? ''}
            </H1>
            <Link
              href={'/list-your-home'}
              className="rounded-[30px] cursor-pointer font-semibold px-8 w-max h-[48px] border-white border-solid border-[0.25px] mx-auto no-underline bg-carolina-blue text-white flex items-center justify-center"
            >
              Get Started
            </Link>
          </div>
        </div>
      </main>
    </>
  );
}

export const revalidate = 600;
