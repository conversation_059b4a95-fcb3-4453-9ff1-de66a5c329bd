import { ReactNode } from 'react';

import HeaderLinks from '@/app/components/common/HeaderLinks';
import UserMenuWrapper from '@/app/components/common/UserMenuWrapper';
import { HOME_ROUTE } from '@/constants/routes';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import Link from 'next/link';

const BackgroundVideo = dynamic(() => import('@/clients/components/common/BackgroundVideo'));

type Props = {
  children: ReactNode;
};

const ListYourHomeHeader = ({ children }: Props) => {
  return (
    <div className="relative w-full h-[800px] lg:h-[875px]">
      <Image
        src="/images/list-your-home-bg.avif"
        alt="list your home background"
        fill
        className="w-full h-full object-cover md:z-0"
        priority
      />
      <BackgroundVideo playListUrl="https://res.cloudinary.com/dpf7sq6of/raw/upload/v1739256726/videos/list-your-home/ieciguicgrbvnabflqgh.m3u8n" />
      <div className="absolute inset-0 !bg-[rgba(0,0,0,0.1)] z-[2]" />
      <div className="absolute inset-0 p-2 z-[2]">
        <div className="container mx-lg">
          <nav className="flex items-center py-4 justify-between">
            <Link href={HOME_ROUTE} className="cursor-pointer">
              <Image alt="NR logo" src="/images/logo-1.svg" width={120} height={40} priority />
            </Link>

            <div className="hidden lg:block">
              <HeaderLinks
                className="border-white"
                linkItemClassName="hover:bg-[rgba(255,255,255,0.20)] text-white"
              />
            </div>

            <UserMenuWrapper />
          </nav>
          {children}
        </div>
      </div>
    </div>
  );
};

export default ListYourHomeHeader;
