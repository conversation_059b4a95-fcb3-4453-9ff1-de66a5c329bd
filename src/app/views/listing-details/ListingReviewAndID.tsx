import { PropertyDetails } from '@/types/properties';
import { numberWithCommas } from '@/utils/common';

type Props = {
  data: PropertyDetails;
};

const ListingReviewAndID = ({ data }: Props) => {
  return (
    <div className="flex items-center justify-between">
      <p className="m-0 pl-2 md:pl-0 text-english-manor italic">NR listing {data.nrPropertyId}</p>
      {data.avg_rating && (
        <div className="flex items-center text-english-manor text-sm md:text-base">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-[18px] h-[18px] md:w-5 md:h-5 text-[#FFB020] mr-2 md:mr-3"
          >
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.86L12 17.77l-6.18 3.25L7 14.14l-5-4.87 6.91-1.01L12 2z" />
          </svg>
          {data?.avg_rating} ({numberWithCommas(data?.ratings_count ?? 0)})
        </div>
      )}
    </div>
  );
};

export default ListingReviewAndID;
