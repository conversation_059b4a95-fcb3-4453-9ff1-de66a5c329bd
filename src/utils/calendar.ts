import groupBy from 'lodash/groupBy';

import { DateRange } from 'react-day-picker';

import { AvailableCalendar, AvailabilityType, MonthList } from '@/types/calendar';
import { RentalRatesCalendarData } from '@/types/properties';

import {
  addMonths,
  addYears,
  differenceInMonths,
  endOfMonth,
  isValid,
  startOfMonth,
  subMonths,
} from 'date-fns';
import dayjs, { Dayjs } from 'dayjs';

export const getDateRangePickerMonths = () => {
  const months: MonthList = [];
  const minDate = startOfMonth(new Date());
  const maxDate = endOfMonth(subMonths(addYears(new Date(), 2), 1));
  const numberOfMonths = differenceInMonths(maxDate, minDate);
  for (let i = 0; i <= numberOfMonths; i++) {
    months.push({
      year: addMonths(minDate, i).getFullYear(),
      month: addMonths(minDate, i).getMonth(),
    });
  }
  return months;
};

export const getMonthYearsList = (year?: number): MonthList => {
  const currentMonth = dayjs(year && `${year}`, `YYYY`).startOf('year');
  return getMonthsBetweenDates(
    currentMonth.format('YYYY-MM-DD'),
    currentMonth.add(11, 'months').format('YYYY-MM-DD'),
  );
};

export const getMonthListByYearForPDF = () => {
  const currentMonth = dayjs();
  return groupBy(
    getMonthsBetweenDates(
      currentMonth.format('YYYY-MM-DD'),
      currentMonth.add(11, 'months').format('YYYY-MM-DD'),
    ) ?? [],
    'year',
  );
};

export const getMonthsBetweenDates = (startDate?: string, endDate?: string) => {
  const months: MonthList = [];
  if (!startDate || !endDate) {
    return months;
  }
  const start = dayjs(startDate, 'YYYY-MM-DD').startOf('month');
  const end = dayjs(endDate, 'YYYY-MM-DD').endOf('month');
  const numberOfMonths = end.diff(start, 'month');

  const current = start;
  for (let i = 0; i <= numberOfMonths; i++) {
    months.push({
      year: current.add(i, 'month').year(),
      month: current.add(i, 'month').month(),
    });
  }

  return months;
};

export const getNumberOfDaysInMonth = (year: number, month: number): number => {
  return dayjs(`${year}-${month + 1}`, 'YYYY-M').daysInMonth();
};

export const getFirstDayOfMonth = (year: number, month: number) => {
  return dayjs(`${year}-${month + 1}`, 'YYYY-M')
    .startOf('month')
    .day();
};

export const isBetweenTwoDates = (
  startDate: Dayjs | string | null,
  endDate: Dayjs | string | null,
  date: Dayjs,
  excludeStartAndEnd?: boolean,
): boolean => {
  const start = dayjs(startDate, 'YYYY-MM-DD').startOf('day');
  const end = dayjs(endDate, 'YYYY-MM-DD').endOf('day');
  if (excludeStartAndEnd) {
    return date.isBetween(start, end);
  }
  return date.isBetween(start, end) || date.isSame(start) || date.isSame(end);
};

export const formatAvailabilityDateString = (date?: string): Dayjs => dayjs(date, 'YYYY-MM-DD');

export const checkIfStringIsValidDate = (dateString: string, format = 'YYYY-MM-DD'): boolean => {
  return dayjs(dateString, format).isValid();
};

export const getAvailabilityDataForCurrentMonth = (
  availabilityData: AvailableCalendar[],
  currentMonth: number,
  currentYear: number,
) => {
  const dateObject = dayjs(`${currentYear}/${currentMonth}/1`, 'YYYY/M/D');
  const minDate = dateObject.subtract(1, 'day');
  const maxDate = dayjs(dateObject).add(1, 'month').startOf('month').add(7, 'days');
  return availabilityData.filter(
    (_a) =>
      dayjs(_a.blockedTo).isBetween(minDate, maxDate) ||
      dayjs(_a.blockedFrom).isBetween(minDate, maxDate) ||
      (dateObject.isBetween(dayjs(_a.blockedFrom), dayjs(_a.blockedTo)) &&
        maxDate.isBetween(dayjs(_a.blockedFrom), dayjs(_a.blockedTo))),
  );
};

export const getRentalRatesForCurrentMonth = (
  rentalRates: RentalRatesCalendarData[],
  currentMonth: number,
  currentYear: number,
) => {
  const dateObject = dayjs(`${currentYear}/${currentMonth}/1`, 'YYYY/M/D');
  const minDate = dateObject.subtract(1, 'day');
  const maxDate = dayjs(dateObject).add(1, 'month').startOf('month');
  return rentalRates.filter((_a) => dayjs(_a.rateStartFrom).isBetween(minDate, maxDate));
};

export const getDateRangeStartAndEnd = (
  range: AvailableCalendar[],
  dateObject: Dayjs,
  index = 0,
) => {
  const { blockedFrom, blockedTo } = range[index] || {};
  const start = dateObject.isSame(formatAvailabilityDateString(blockedFrom));
  const end = dateObject.isSame(formatAvailabilityDateString(blockedTo));
  return [range.length > 0, start, end];
};

export const getAvailabilityTitle = (blockType: AvailabilityType) => {
  switch (blockType) {
    case AvailabilityType.OWNER_TIME:
      return 'Owner time';
    case AvailabilityType.LEASED:
      return 'Leased';
    default:
      return 'Other';
  }
};

export const getAvailabilityDataForRange = (
  availabilityData: AvailableCalendar[],
  startDate: Dayjs | string | null,
  endDate: Dayjs | string | null,
) => {
  return availabilityData.filter(({ blockedFrom, blockedTo }) => {
    if (startDate && !endDate) {
      return (
        dayjs(blockedFrom).isSame(startDate) ||
        dayjs(blockedTo).isSame(startDate) ||
        isBetweenTwoDates(blockedFrom, blockedTo, dayjs(startDate))
      );
    }

    if (startDate && endDate) {
      return (
        dayjs(blockedFrom).isSame(startDate) ||
        dayjs(blockedTo).isSame(startDate) ||
        isBetweenTwoDates(startDate, endDate, dayjs(blockedFrom)) ||
        isBetweenTwoDates(startDate, endDate, dayjs(blockedTo))
      );
    }
  });
};

export const checkIfTwoRangesOverlaps = (
  range1: [string, string],
  range2: [string, string],
): boolean => {
  const start = dayjs(range1[0], 'YYYY-MM-DD').startOf('day');
  const end = dayjs(range1[1], 'YYYY-MM-DD').startOf('day');
  const start2 = dayjs(range2[0], 'YYYY-MM-DD').startOf('day');
  const end2 = dayjs(range2[1], 'YYYY-MM-DD').startOf('day');
  return (
    start.isBetween(start2, end2) ||
    end.isBetween(start2, end2) ||
    start2.isBetween(start, end) ||
    end2.isBetween(start, end)
  );
};

export const getRentalRateDataForMonth = (
  rentalRates: RentalRatesCalendarData[],
  month: number,
  year: number,
) => {
  const dateObject = dayjs(`${year}/${month + 1}/1`, 'YYYY/M/D');
  const minDate = dateObject;
  const maxDate = dayjs(dateObject).add(1, 'month').startOf('month').add(1, 'week');
  return rentalRates.filter(({ rateStartFrom, rateEndOn }) => {
    return (
      dayjs(rateStartFrom).isSame(minDate) ||
      dayjs(rateEndOn).isSame(minDate) ||
      isBetweenTwoDates(minDate, maxDate, dayjs(rateStartFrom)) ||
      isBetweenTwoDates(minDate, maxDate, dayjs(rateEndOn))
    );
  });
};

export const getWeeklyRates = (
  rentalRates: RentalRatesCalendarData[],
  turnoverDayNumber: number,
) => {
  if (rentalRates.length === 0) {
    return [];
  }
  return rentalRates
    .map(({ rateStartFrom, propertyWeeklyRent }) => {
      if (dayjs(rateStartFrom).get('day') === turnoverDayNumber) {
        return {
          start: dayjs(rateStartFrom).format('MM/DD'),
          end: dayjs(rateStartFrom).add(1, 'week').format('MM/DD'),
          rate: propertyWeeklyRent,
        };
      }
    })
    .filter((_m) => !!_m);
};

export const isDateRangeSelected = (range?: DateRange) =>
  range?.from && range?.to && isValid(range.from) && isValid(range.to);
