import uniq from 'lodash/uniq';

import { DateRange as RDP_DateRange } from 'react-day-picker';

import { RentalRatesCalendarData } from '@/redux/interfaces/properties';
import {
  AvailabilityCalendarData,
  BlockedType,
  RentalRatesCalendarData as RentalRatesData,
} from '@/types/properties';
import { DateRange } from '@mui/x-date-pickers-pro';

import { startOfWeek, endOfWeek, eachDayOfInterval, addWeeks } from 'date-fns';
import dayjs, { Dayjs } from 'dayjs';

import { isBetweenTwoDates } from './availabilityCalender';

const DAY_NAMES = dayjs.weekdays();

export const getWeekDaysForDate = (date: Date, numberOfWeeks: number) => {
  const weekStart = startOfWeek(date, { weekStartsOn: 0 });
  const weekEnd = endOfWeek(addWeeks(date, numberOfWeeks - 1), { weekStartsOn: 0 });
  return eachDayOfInterval({ start: weekStart, end: weekEnd });
};

export const getRentalRatesForCurrentMonth = (
  rentalRates: RentalRatesCalendarData[],
  currentMonth: number,
  currentYear: number,
) => {
  const dateObject = dayjs(`${currentYear}/${currentMonth}/1`, 'YYYY/M/D');
  const minDate = dateObject.subtract(1, 'day');
  const maxDate = dayjs(dateObject).add(1, 'month').startOf('month');
  return rentalRates.filter((_a) => dayjs(_a.rateStartFrom).isBetween(minDate, maxDate));
};

export const getPropertyMonthlyTurnoverDayLabel = (
  currentMonth: number,
  currentYear: number,
  rentalRates: RentalRatesCalendarData[],
) => {
  const rentalRatesForTheMonth = getRentalRatesForCurrentMonth(
    rentalRates,
    currentMonth,
    currentYear,
  );

  const turnoverDays = rentalRatesForTheMonth.filter((_r) => _r.allowCheckin);

  const turnoverDaysLabel = turnoverDays.reduce((acc: string[], curr) => {
    const day = DAY_NAMES[dayjs(curr.rateStartFrom, 'YYYY-MM-DD').get('day')];
    return uniq([...acc, day]);
  }, []);

  return turnoverDaysLabel.length === 7 ? 'Any day' : turnoverDaysLabel.join(', ');
};

export const getMinimumNightStaysForRange = (
  start: Dayjs,
  rentalRanges: RentalRatesCalendarData[],
) => {
  return rentalRanges.find((_r) => dayjs(_r.rateStartFrom).isSame(start))?.minimumNightsStay;
};

export const checkIfDateIsDisable = (
  date: Dayjs,
  selectedRangeValue: DateRange<Dayjs>,
  isCheckinDay = false,
  rentalData?: RentalRatesCalendarData,
): boolean => {
  if (!rentalData) {
    return true;
  }

  if (isCheckinDay && selectedRangeValue?.[0] === null) {
    return false;
  }

  if (selectedRangeValue?.[0] !== null) {
    if (rentalData?.allowCheckout) {
      return false;
    } else {
      return true;
    }
  }

  return true;
};

export const checkIfDateHasRate = (
  date: Dayjs,
  rentalRates: RentalRatesCalendarData[] = [],
): boolean => {
  return (
    rentalRates.filter(({ rateStartFrom, rateEndOn }) =>
      isBetweenTwoDates(rateStartFrom, rateEndOn, date),
    ).length > 0
  );
};

export const checkIfDateHasNightlyRate = (
  date: Dayjs,
  rentalRates: RentalRatesCalendarData[] = [],
) => {
  return !!rentalRates.find((_r) => dayjs(_r.rateStartFrom).isSame(dayjs(date)));
};

export const checkIfDateIsCheckinday = (
  date: Dayjs,
  rentalRates: RentalRatesCalendarData[] = [],
) => {
  return !!rentalRates.find((_r) => dayjs(_r.rateStartFrom).isSame(date))?.allowCheckin;
};

export const checkIfDateHasAnyCheckinDay = (
  date: Dayjs,
  rentalRates: RentalRatesCalendarData[] = [],
) => {
  const startOfWeek = dayjs(date).startOf('month');
  const endOfWeek = dayjs(date).endOf('month');
  const ratesForTheWeek = rentalRates.filter(
    (_r) =>
      dayjs(_r.rateStartFrom).isBetween(startOfWeek, endOfWeek) ||
      dayjs(_r.rateStartFrom).isSame(startOfWeek) ||
      dayjs(_r.rateStartFrom).isSame(endOfWeek),
  );
  const hasNoCheckinDays = ratesForTheWeek.every((_r) => !_r.allowCheckin);
  return hasNoCheckinDays;
};
