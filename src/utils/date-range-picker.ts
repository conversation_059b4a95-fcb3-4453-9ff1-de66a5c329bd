import { DateRange as RDP_DateRange } from 'react-day-picker';

import { Nullable } from '@/types/common';
import { AvailabilityCalendarData, BlockedType, RentalRatesCalendarData } from '@/types/properties';

import {
  addDays,
  addMonths,
  differenceInDays,
  format,
  isAfter,
  isBefore,
  isSameDay,
  isSameMonth,
  isWithinInterval,
  parse,
  startOfDay,
  startOfMonth,
  subDays,
} from 'date-fns';

import { parseDateString } from './common';

export const DAY_NAMES = [
  'Sunday',
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
];

export const isBetweenTwoDates = (startDate: string, endDate: string, date: Date): boolean => {
  const start = parse(startDate, 'yyyy-MM-dd', new Date());
  const end = parse(endDate, 'yyyy-MM-dd', new Date());

  return (
    isWithinInterval(date, {
      start: start,
      end: end,
    }) ||
    isSameDay(date, start) ||
    isSameDay(date, end)
  );
};

export const getBookedAndBlockedRangesForDate = (
  availabilityRanges: AvailabilityCalendarData[] = [],
  date: Date,
) => {
  const bookedRanges: AvailabilityCalendarData[] = [];
  const blockedRanges: AvailabilityCalendarData[] = [];

  for (const range of availabilityRanges) {
    const { blockedFrom, blockedTo, blockedType } = range;
    if (isBetweenTwoDates(blockedFrom, blockedTo, date)) {
      if (blockedType === BlockedType.LEASED) {
        bookedRanges.push(range);
      } else {
        blockedRanges.push(range);
      }
    }
  }

  return [bookedRanges, blockedRanges];
};

export const checkIfDateCannotBeCheckedIn = (
  date: Date,
  checkinDay: Nullable<string>,
  rentalData?: RentalRatesCalendarData,
  rentalDataForStartDate?: RentalRatesCalendarData,
) => {
  // console.log('check in day', date, checkinDay);
  if (
    !rentalData ||
    (!rentalDataForStartDate && !rentalData.allowCheckin) ||
    (rentalDataForStartDate &&
      (!rentalData.allowCheckout || (checkinDay && DAY_NAMES[date.getDay()] !== checkinDay)))
  ) {
    return true;
  }

  if (
    rentalDataForStartDate &&
    rentalDataForStartDate.minimumNightsStay &&
    isAfter(new Date(date), parseDateString(rentalDataForStartDate.rateStartFrom)) &&
    differenceInDays(
      parseDateString(rentalData.rateStartFrom),
      parseDateString(rentalDataForStartDate.rateStartFrom),
    ) < rentalDataForStartDate.minimumNightsStay
  ) {
    return true;
  }

  return false;
};

export const checkIfDateDisabledDesktop = (
  checkinDay: Nullable<string>,
  firstCheckinDt: Nullable<Date>,
  lastCheckinDt: Nullable<Date>,
  selectedRangeValue?: RDP_DateRange,
  rentalData?: RentalRatesCalendarData,
  availabilityData: AvailabilityCalendarData[] = [],
): boolean => {
  if (!rentalData) {
    return true;
  }

  if (
    !selectedRangeValue?.from &&
    ((firstCheckinDt && isBefore(parseDateString(rentalData.rateStartFrom), firstCheckinDt)) ||
      (lastCheckinDt && isAfter(parseDateString(rentalData.rateStartFrom), lastCheckinDt)))
  ) {
    return false;
  }

  if (!checkinDay && !rentalData?.allowCheckin && !selectedRangeValue?.from) {
    return true;
  }

  if (selectedRangeValue?.from) {
    if (
      (!rentalData.allowCheckout && !checkinDay) ||
      isBefore(parseDateString(rentalData.rateStartFrom), selectedRangeValue?.from) ||
      availabilityData.some((_range) =>
        rangesOverlap(
          {
            from: parseDateString(_range.blockedFrom),
            to: parseDateString(_range.blockedTo),
          },
          {
            from: selectedRangeValue?.from,
            to: parseDateString(rentalData.rateStartFrom),
          },
        ),
      )
    ) {
      return true;
    }

    return false;
  }

  return false;
};

export const checkIfDateDisabled = (
  selectedRangeValue?: RDP_DateRange,
  rentalData?: RentalRatesCalendarData,
  rentalDataForStartDate?: RentalRatesCalendarData,
  availabilityData: AvailabilityCalendarData[] = [],
): boolean => {
  if (!rentalData) {
    return true;
  }

  if (selectedRangeValue?.from === null) {
    return false;
  }

  if (selectedRangeValue?.from !== null) {
    if (!rentalData?.allowCheckout) {
      return true;
    } else {
      if (
        selectedRangeValue?.from &&
        isBefore(parseDateString(rentalData.rateStartFrom), selectedRangeValue?.from)
      ) {
        return true;
      }
      if (
        rentalDataForStartDate &&
        rentalDataForStartDate.minimumNightsStay &&
        selectedRangeValue?.from &&
        isAfter(parseDateString(rentalData.rateStartFrom), selectedRangeValue?.from) &&
        differenceInDays(
          parseDateString(rentalData.rateStartFrom),
          parseDateString(rentalDataForStartDate.rateStartFrom),
        ) < rentalDataForStartDate.minimumNightsStay
      ) {
        return true;
      }
      if (
        availabilityData.some((_range) =>
          rangesOverlap(
            {
              from: parseDateString(_range.blockedFrom),
              to: parseDateString(_range.blockedTo),
            },
            {
              from: selectedRangeValue?.from,
              to: parseDateString(rentalData.rateStartFrom),
            },
          ),
        )
      ) {
        return true;
      }
      return false;
    }
  }

  return true;
};

export const getAvailabilityDataForCurrentMonth = (
  availabilityData: AvailabilityCalendarData[],
  currentMonth: number,
  currentYear: number,
) => {
  const dateObject = parseDateString(`${currentYear}/${currentMonth}/01`);
  const minDate = subDays(dateObject, 1);
  const maxDate = addDays(startOfMonth(addMonths(dateObject, 1)), 7);
  return availabilityData.filter(
    (_a) =>
      isWithinInterval(parseDateString(_a.blockedTo), {
        start: minDate,
        end: maxDate,
      }) ||
      isWithinInterval(parseDateString(_a.blockedFrom), {
        start: minDate,
        end: maxDate,
      }) ||
      (isWithinInterval(dateObject, {
        start: parseDateString(_a.blockedFrom),
        end: parseDateString(_a.blockedTo),
      }) &&
        isWithinInterval(maxDate, {
          start: parseDateString(_a.blockedFrom),
          end: parseDateString(_a.blockedTo),
        })),
  );
};

export const getRentalRatesForCurrentMonth = (
  rentalRates: RentalRatesCalendarData[],
  currentMonth: number,
  currentYear: number,
) => {
  const dateObject = parseDateString(`${currentYear}/${currentMonth}/01`);
  const minDate = subDays(dateObject, 1);
  const maxDate = startOfMonth(addMonths(dateObject, 1));
  return rentalRates.filter((_a) =>
    isWithinInterval(parseDateString(_a.rateStartFrom), {
      start: minDate,
      end: maxDate,
    }),
  );
};

export const getDateRangeStartAndEnd = (
  range: AvailabilityCalendarData[],
  dateObject: Date,
  index = 0,
) => {
  const { blockedFrom, blockedTo } = range[index] || {};
  const start = isSameDay(dateObject, parseDateString(blockedFrom));
  const end = isSameDay(dateObject, parseDateString(blockedTo));
  return [range.length > 0, start, end];
};

export const rangesOverlap = (range1: RDP_DateRange, range2: RDP_DateRange): boolean => {
  if (!range1?.from || !range1?.to || !range2?.from || !range2?.to) {
    return false;
  }
  return (
    isBefore(startOfDay(range1?.from), startOfDay(range2?.to)) &&
    isBefore(startOfDay(range2?.from), startOfDay(range1?.to))
  );
};

export const getDatesBetweenDates = (startDate?: Nullable<Date>, endDate?: Nullable<Date>) => {
  const daysArray: Date[] = [];
  if (!startDate || !endDate) {
    return daysArray;
  }
  const numberOfDays = differenceInDays(endDate, startDate);

  for (let i = 1; i <= numberOfDays; i++) {
    daysArray.push(addDays(startDate, 1 * i));
  }

  return daysArray;
};

export const checkIfDateHasRate = (
  date: Date,
  rentalRates: RentalRatesCalendarData[] = [],
): boolean => {
  return (
    rentalRates.filter(({ rateStartFrom, rateEndOn }) =>
      isBetweenTwoDates(rateStartFrom, rateEndOn, date),
    ).length > 0
  );
};

export const getBlockedStartAndEndDates = (availabilityRanges: AvailabilityCalendarData[] = []) => {
  const start: Date[] = [];
  const end: Date[] = [];

  availabilityRanges.map((_r) => {
    const { blockedFrom, blockedTo } = _r;
    start.push(parseDateString(blockedFrom));
    end.push(parseDateString(blockedTo));
  });

  return {
    start,
    end,
  };
};

export const getCheckinDayRelativeToADate = (
  rates: RentalRatesCalendarData[],
  date: Date,
  availabilityData: AvailabilityCalendarData[],
) => {
  if (isBefore(startOfDay(date), startOfDay(new Date()))) {
    return null;
  }

  const checkinDays = rates?.filter(
    (_r) => _r.allowCheckin && !isBefore(parseDateString(_r.rateStartFrom), startOfDay(new Date())),
  );

  // console.log('checkin days', checkinDays, date, rates);

  if (
    checkinDays.every(
      (_d) =>
        parseDateString(_d.rateStartFrom).getDay() ===
        parseDateString(checkinDays[0].rateStartFrom).getDay(),
    )
  ) {
    return DAY_NAMES[parseDateString(checkinDays[0]?.rateStartFrom ?? '').getDay()];
  }

  return null;

  // const indexOfCurrentDt = rates.findIndex((_r) => isSameDay(new Date(_r.rateStartFrom), date));
  // const last7Rates = rates.slice(Math.max(indexOfCurrentDt - 7, 0), indexOfCurrentDt);

  // const next7Rates = rates.slice(indexOfCurrentDt + 1, Math.max(indexOfCurrentDt + 8, 0));

  // if (last7Rates.every((_r) => _r.allowCheckin) || next7Rates.every((_r) => _r.allowCheckin)) {
  //   return null;
  // }

  // const lastAllowedCheckin = last7Rates.find((_r) => _r.allowCheckin);
  // const nextAllowedCheckin = next7Rates.find((_r) => _r.allowCheckin);

  // if (
  //   (lastAllowedCheckin &&
  //     new Date(lastAllowedCheckin.rateStartFrom).getDay() === date.getDay() &&
  //     isSameMonth(new Date(lastAllowedCheckin.rateStartFrom), date)) ||
  //   (nextAllowedCheckin &&
  //     new Date(nextAllowedCheckin.rateStartFrom).getDay() === date.getDay() &&
  //     isSameMonth(new Date(nextAllowedCheckin.rateStartFrom), date))
  // ) {
  //   return DAY_NAMES[date.getDay()];
  // }

  // if (
  //   (lastAllowedCheckin && isSameMonth(new Date(lastAllowedCheckin.rateStartFrom), date)) ||
  //   (nextAllowedCheckin && isSameMonth(new Date(nextAllowedCheckin.rateStartFrom), date))
  // ) {
  //   return !isBefore(
  //     startOfDay(new Date(lastAllowedCheckin.rateStartFrom)),
  //     startOfDay(new Date()),
  //   ) &&
  //     new Date(lastAllowedCheckin.rateStartFrom).getDay() ===
  //       new Date(nextAllowedCheckin.rateStartFrom).getDay()
  //     ? DAY_NAMES[new Date(lastAllowedCheckin.rateStartFrom).getDay()]
  //     : null;
  // }

  // return null;
};

export const isForcedCheckinDay = (date: Date, rentalRates: RentalRatesCalendarData[]) => {
  if (isBefore(startOfDay(date), startOfDay(new Date()))) {
    return null;
  }

  const checkinDays = rentalRates?.filter(
    (_r) => _r.allowCheckin && !isBefore(parseDateString(_r.rateStartFrom), startOfDay(new Date())),
  );

  return checkinDays.every(
    (_d) =>
      parseDateString(_d.rateStartFrom).getDay() ===
      parseDateString(checkinDays[0].rateStartFrom).getDay(),
  )
    ? !!checkinDays.some((_ck) =>
        isSameDay(startOfDay(date), startOfDay(parseDateString(_ck.rateStartFrom))),
      )
    : false;
};

export const isDateBetween = (date: Date, startDate: Date, endDate: Date) => {
  return isWithinInterval(date, { start: startDate, end: endDate });
};

export const getBlockedRangesForDate = (
  availabilityRanges: AvailabilityCalendarData[] = [],
  date: Date,
) => availabilityRanges.filter((_r) => isBetweenTwoDates(_r.blockedFrom, _r.blockedTo, date));

export const formatDateRangePickerRentalRates = (rentalRates: RentalRatesCalendarData[]) =>
  [...Array(24).keys()].reduce((acc, num) => {
    const currentDt = addMonths(startOfMonth(startOfDay(new Date())), num);
    return {
      ...acc,
      [format(currentDt, 'MMM-yyyy')]: rentalRates.filter((_r) =>
        isSameMonth(currentDt, parseDateString(_r.rateStartFrom)),
      ),
    };
  }, {} as { [_s: string]: RentalRatesCalendarData[] });

export const getStartAndEndForcedCheckinDay = (rentalRates: RentalRatesCalendarData[]) => {
  const checkinDays = rentalRates?.filter(
    (_r) => _r.allowCheckin && !isBefore(parseDateString(_r.rateStartFrom), startOfDay(new Date())),
  );
  const firstCheckinDay = checkinDays[0];
  const lastCheckinDay = checkinDays[checkinDays.length - 1];

  return checkinDays.every(
    (_d) =>
      parseDateString(_d.rateStartFrom).getDay() ===
      parseDateString(checkinDays[0].rateStartFrom).getDay(),
  ) &&
    firstCheckinDay &&
    lastCheckinDay
    ? [
        parseDateString(firstCheckinDay.rateStartFrom),
        parseDateString(lastCheckinDay.rateStartFrom),
      ]
    : [null, null];
};
