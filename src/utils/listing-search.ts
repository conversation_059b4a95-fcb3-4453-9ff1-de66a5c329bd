import isArray from 'lodash/isArray';

import { addDays, format, isValid, isSameMonth } from 'date-fns';

import { parseDateString } from './common';

export const getSearchParamsForPropertyCard = (searchParams: { [key: string]: string }) => {
  const params = [];
  for (const [key, value] of Object.entries(searchParams)) {
    if (key === 'date_min') {
      params.push(`from=${value}`);
      continue;
    }
    if (key === 'date_max') {
      params.push(`to=${value}`);
      continue;
    }
    if (key === 'adults' || key === 'children') {
      params.push(`${key}=${value}`);
      continue;
    }
  }

  return params.join('&');
};

export const getListingSearchQueriesFromURL = (searchParams: { [key: string]: string }) => {
  const params = [];
  for (const [key, value] of Object.entries(searchParams)) {
    if (key === 'rent_min') {
      params.push(`rent_range_min=${value}`);
      continue;
    }
    if (key === 'rent_max') {
      params.push(`rent_range_max=${value}`);
      continue;
    }
    if (key === 'bedrooms') {
      params.push(`bedroom_min=${value}`);
      continue;
    }
    if (key === 'adults') {
      const totalGuest = Number(value) + Number(searchParams['children'] ?? 0);
      params.push(`guest_capacity_min=${totalGuest}`);
      continue;
    }
    if (key === 'children') {
      continue;
    }
    if (key === 'date_min') {
      params.push(`dateFrom=${value}`);
      continue;
    }
    if (key === 'date_max') {
      params.push(`dateTo=${value}`);
      continue;
    }
    if (isArray(value)) {
      value.forEach((x) => params.push(`${key}=${x}`));
    } else {
      params.push(`${key}=${value}`);
    }
  }
  const str = params.join('&');
  return str;
};

export const getSearchParamsForAdditionalPropertyCard = (
  searchParams: {
    [key: string]: string;
  },
  checkinDt: Date,
  minNightStay: number,
) => {
  const params = [];
  for (const [key, value] of Object.entries(searchParams)) {
    if (key === 'adults' || key === 'children') {
      params.push(`${key}=${value}`);
      continue;
    }
  }

  params.push(`from=${format(checkinDt, 'yyyy-MM-dd')}`);
  params.push(`to=${format(addDays(checkinDt, minNightStay), 'yyyy-MM-dd')}`);

  return params.join('&');
};

export const getDateRangeStringMobileHeader = (date_min?: string, date_max?: string) => {
  const hasDateQueries = !!(
    date_min &&
    isValid(parseDateString(date_min)) &&
    date_max &&
    isValid(parseDateString(date_max))
  );

  if (hasDateQueries) {
    const sameMonth = isSameMonth(parseDateString(date_min), parseDateString(date_max));
    return sameMonth
      ? `${format(parseDateString(date_min), 'MMM d')} - ${format(parseDateString(date_max), 'd')}`
      : `${format(parseDateString(date_min), 'MMM d')} - ${format(
          parseDateString(date_max),
          'MMM d',
        )}`;
  } else {
    return 'Anytime';
  }
};
