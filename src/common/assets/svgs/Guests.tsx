import * as React from 'react';
import { SVGProps } from 'react';

const SvgGuests = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={21}
    height={21}
    fill="none"
    viewBox="0 0 21 21"
    {...props}
  >
    <path
      stroke={props.stroke || '#0EA5E9'}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={1.25}
      d="M14.118 17.8v-1.668a3.335 3.335 0 0 0-3.333-3.333h-5a3.335 3.335 0 0 0-3.333 3.334v1.666M14.118 2.906a3.333 3.333 0 0 1 0 6.453M19.118 17.8v-1.668a3.334 3.334 0 0 0-2.5-3.225M4.952 6.133a3.335 3.335 0 0 0 3.333 3.333 3.335 3.335 0 0 0 3.333-3.333 3.335 3.335 0 0 0-3.333-3.334 3.335 3.335 0 0 0-3.333 3.334"
    />
  </svg>
);
export default SvgGuests;
