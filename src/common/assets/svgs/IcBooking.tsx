import * as React from 'react';

const IcBooking = (props: React.JSX.IntrinsicAttributes & React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={37}
    height={37}
    viewBox="0 0 37 37"
    fill="none"
    {...props}
  >
    <path
      fill="#8DE1FF"
      stroke="#6D7380"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      d="M34.801 31.262H5.123c-.374 0-.714-.338-.748-.743L1.587 4.759c-.034-.439.612-.776.986-.776h29.135c.374 0 .714.337.748.743l2.89 25.793c.067.405-.17.743-.544.743Z"
    />
    <path
      fill="#fff"
      stroke="#6D7380"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      d="M31.504 35.38c.374 0 .68-.303.68-.708V4.692c0-.372-.306-.71-.68-.71H2.267c-.374 0-.68.305-.68.71v30.013c0 .372.306.71.68.71h29.237v-.034Z"
    />
    <path
      fill="#8DE1FF"
      stroke="#6D7380"
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      d="M32.184 9.418H1.587v-4.76a.68.68 0 0 1 .68-.675h29.237a.68.68 0 0 1 .68.675v4.76Z"
    />
    <path
      fill="#8DE1FF"
      d="M16.851 31.836c.752 0 1.36-.605 1.36-1.35 0-.747-.608-1.351-1.36-1.351-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM12.772 31.836c.75 0 1.36-.605 1.36-1.35 0-.747-.61-1.351-1.36-1.351-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM8.692 31.836c.751 0 1.36-.605 1.36-1.35 0-.747-.609-1.351-1.36-1.351-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM4.613 31.836c.75 0 1.36-.605 1.36-1.35 0-.747-.61-1.351-1.36-1.351-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM29.09 27.784c.751 0 1.36-.604 1.36-1.35 0-.746-.609-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM25.01 27.784c.752 0 1.36-.604 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM20.93 27.784c.752 0 1.36-.604 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.359.604-1.359 1.35 0 .746.609 1.35 1.36 1.35ZM16.851 27.784c.752 0 1.36-.604 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM12.772 27.784c.75 0 1.36-.604 1.36-1.35 0-.746-.61-1.35-1.36-1.35-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM8.692 27.784c.751 0 1.36-.604 1.36-1.35 0-.746-.609-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM4.613 27.784c.75 0 1.36-.604 1.36-1.35 0-.746-.61-1.35-1.36-1.35-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM29.09 23.733c.751 0 1.36-.605 1.36-1.35 0-.746-.609-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .745.61 1.35 1.36 1.35Z"
    />
    <path
      fill="#fff"
      d="M25.01 23.733c.752 0 1.36-.605 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .745.61 1.35 1.36 1.35ZM20.93 23.733c.752 0 1.36-.605 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.359.604-1.359 1.35 0 .745.609 1.35 1.36 1.35ZM16.851 23.733c.752 0 1.36-.605 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .745.61 1.35 1.36 1.35Z"
    />
    <path
      fill="#8DE1FF"
      d="M12.772 23.733c.75 0 1.36-.605 1.36-1.35 0-.746-.61-1.35-1.36-1.35-.751 0-1.36.604-1.36 1.35 0 .745.609 1.35 1.36 1.35ZM8.692 23.733c.751 0 1.36-.605 1.36-1.35 0-.746-.609-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .745.61 1.35 1.36 1.35ZM4.613 23.733c.75 0 1.36-.605 1.36-1.35 0-.746-.61-1.35-1.36-1.35-.751 0-1.36.604-1.36 1.35 0 .745.609 1.35 1.36 1.35ZM29.09 19.682c.751 0 1.36-.605 1.36-1.35 0-.747-.609-1.351-1.36-1.351-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM25.01 19.682c.752 0 1.36-.605 1.36-1.35 0-.747-.608-1.351-1.36-1.351-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM20.93 19.682c.752 0 1.36-.605 1.36-1.35 0-.747-.608-1.351-1.36-1.351-.75 0-1.359.604-1.359 1.35 0 .746.609 1.35 1.36 1.35ZM16.851 19.682c.752 0 1.36-.605 1.36-1.35 0-.747-.608-1.351-1.36-1.351-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM12.772 19.682c.75 0 1.36-.605 1.36-1.35 0-.747-.61-1.351-1.36-1.351-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM8.692 19.682c.751 0 1.36-.605 1.36-1.35 0-.747-.609-1.351-1.36-1.351-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM4.613 19.682c.75 0 1.36-.605 1.36-1.35 0-.747-.61-1.351-1.36-1.351-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM29.09 15.63c.751 0 1.36-.604 1.36-1.35 0-.746-.609-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM25.01 15.63c.752 0 1.36-.604 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35ZM20.931 15.63c.751 0 1.36-.604 1.36-1.35 0-.746-.609-1.35-1.36-1.35-.751 0-1.36.604-1.36 1.35 0 .746.609 1.35 1.36 1.35ZM16.851 15.63c.752 0 1.36-.604 1.36-1.35 0-.746-.608-1.35-1.36-1.35-.75 0-1.36.604-1.36 1.35 0 .746.61 1.35 1.36 1.35Z"
    />
    <path
      fill="#15A5E5"
      stroke="#6D7380"
      strokeMiterlimit={10}
      d="M6.788 6.312a.68.68 0 0 1-.68-.675V2.261a.68.68 0 0 1 1.36 0v3.376a.68.68 0 0 1-.68.675ZM26.982 6.312a.68.68 0 0 1-.68-.675V2.261a.68.68 0 0 1 1.36 0v3.376a.68.68 0 0 1-.68.675Z"
    />
    <path
      fill="#15A5E5"
      d="M16.58 23.564a.31.31 0 0 1-.238-.101l-.748-.743a.325.325 0 0 1 0-.473.33.33 0 0 1 .476 0l.51.507 1.461-1.452a.33.33 0 0 1 .476 0 .325.325 0 0 1 0 .473l-1.7 1.688c-.034.067-.136.101-.238.101ZM20.693 23.564a.31.31 0 0 1-.238-.101l-.748-.743a.325.325 0 0 1 0-.473.33.33 0 0 1 .476 0l.51.507 1.462-1.452a.33.33 0 0 1 .476 0 .325.325 0 0 1 0 .473l-1.7 1.688a.31.31 0 0 1-.238.101ZM24.705 23.564a.31.31 0 0 1-.238-.101l-.748-.743a.325.325 0 0 1 0-.473.33.33 0 0 1 .476 0l.51.507 1.461-1.452a.33.33 0 0 1 .476 0 .325.325 0 0 1 0 .473l-1.7 1.688a.31.31 0 0 1-.237.101Z"
    />
  </svg>
);
export default IcBooking;
