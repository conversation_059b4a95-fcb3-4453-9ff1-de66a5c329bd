export type MonthList = {
  month: number;
  year: number;
}[];

export enum AvailabilityType {
  OWNER_TIME = 'owner',
  LEASED = 'leased',
  OTHER = 'other',
}

export type AvailableCalendar = {
  nrPropAvailableCalendarId: number;
  nrPropertyId: number;
  blockedType: AvailabilityType;
  blockedFrom: string;
  blockedTo: string;
};

export type DatePickerInfoPopUp = {
  bottom?: number;
  left?: number;
  right?: number;
  text?: string;
};
