import { Nullable } from './common';

export type LandingContent = {
  id: number;
  heading: string;
  subheading: Nullable<string>;
  background_image_desktop: Nullable<string>;
  background_image_mobile: Nullable<string>;
  background_image_ipad: Nullable<string>;
  section_name: string;
};

export type LandingLinks = {
  id: number;
  title: string;
  category: string;
  link: string;
  image: Nullable<string>;
  order: number;
};

export type PopularSearches = {
  id: number;
  title: 'Homes with Pools';
  link: string;
  image: Nullable<string>;
  order: number;
};

export enum SECTION_NAME {
  SECTION_1 = 'section-1',
  SECTION_2 = 'section-2',
  SECTION_3 = 'section-3',
  SECTION_4 = 'section-4',
  SECTION_5 = 'section-5',
  SECTION_6 = 'section-6',
}
