export enum ProgressStatus {
  LOADING = 'LOADING',
  UPDATING = 'UPDATING',
  DELETING = 'DELETING',
  SUCCESSFUL = 'SUCCESSFUL',
  FAILED = 'FAILED',
}

export type IPagination = {
  count: number;
  currentPage: number;
  next?: string;
  previous?: string;
  totalCount: number;
};

export type Nullable<T> = T | null;

export type EntityID = string | number;

export interface WYSIWYGPage {
  id: number;
  page: string;
  titleTag: string;
  metaDescription: string;
  metaRobots: string;
  metaCanonical: string;
  created: string;
  modified: string;
  name: string;
  slug: string;
  description: string;
}
