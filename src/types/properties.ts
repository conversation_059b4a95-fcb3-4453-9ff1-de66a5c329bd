import { Nullable } from './common';

export enum PropertyMediaType {
  PIC = 'pic',
  VIDEO = 'video',
  TOUR = 'tour',
  FLOOR = 'floor',
}

export interface PropertyPic {
  type?: PropertyMediaType.PIC;
  caption: string;
  created: string;
  modified: string;
  nrPropertyId: number;
  nrPropertyPicId: number;
  nrPropertyPicPath: string;
  thumbnail?: string;
  order?: number;
}

export interface PropertyVideo {
  type: PropertyMediaType.VIDEO;
  caption: string;
  created: string;
  modified: string;
  nrPropertyVideoId: number;
  nrPropertyId: number;
  nrPropertyVideoPath: string;
}

export interface RentalRates {
  NrPropRentRateId: number;
  note: string;
  nrPropertyId: number;
  propertyWeeklyRent: number;
  propertyWeeklyRentInteger: number;
  rateEndOn: string;
  rateStartFrom: string;
  minimumNightsStay: number;
  rate: string;
}

export interface NRProperties {
  NrPropertyPics: PropertyPic[];
  NrPropertyVideo: PropertyVideo[];
  NrRentalRates: RentalRates[];
  activeFlag: boolean;
  created: string;
  description: string;
  disputeFlag: boolean;
  distToBeach: number;
  distToTheHub: number;
  headline: string;
  inclAttic: boolean;
  inclBasement: boolean;
  latitude: number;
  listingId: number;
  locationWaterFront: boolean;
  locationWaterview: boolean;
  longitude: number;
  modified: string;
  neighborhood: string;
  neighborhoodId: number;
  nrPmoId: number;
  nrPropertyId: number;
  slug: string;
  nrVerification: boolean;
  scamFlag: boolean;
  streetAddress: string;
  totalBathrooms: number;
  totalBedrooms: number;
  totalCapacity: number;
  totalDiningrooms: number;
  totalFloors: number;
  totalKitchens: number;
  totalLivingArea: number;
  totalParking: number;
  is_favourite?: boolean;
  hasPool?: boolean;
  has3dTour?: boolean;
  averageNightlyRate?: number;
  avg_rating?: number;
  ratings_count?: number;
}

export type RentalRatesCalendarData = {
  nrPropertyId: number;
  NrPropRentRateId: number;
  rateStartFrom: string;
  rateEndOn: string;
  propertyWeeklyRent: number;
  propertyWeeklyRentInteger?: number;
  note?: string;
  created?: string;
  modified?: string;
  isNightlyRate?: boolean;
  allowCheckin?: Nullable<boolean>;
  allowCheckout?: Nullable<boolean>;
  minimumNightsStay?: Nullable<number>;
};

export interface Bedroom {
  activeFlag: boolean;
  attachBathFlag: boolean;
  capacity: number;
  created: string;
  floorNumber: string;
  modified: string;
  nrPropBedroomId: number;
  nrPropertyId: number;
  title: string;
  nr_property_NrPropSleepArrange: {
    nrPropSleepArrangementId: number;
    bedTypeId: number;
    bedType: string;
    totalBeds: number;
  }[];
}

export interface Property3dTour {
  type: PropertyMediaType.TOUR;
  nrProperty3dTourId: number;
  nrPropertyId: number;
  nrProperty3dTourPath: string;
  caption: string;
}

export interface PropertyFloorPlan {
  type: PropertyMediaType.FLOOR;
  nrPropertyFloorId: number;
  nrPropertyFloorPlanId: number;
  nrPropertyId: number;
  nrPropFloorPlan: string;
  caption: string;
}

export interface PropertyDetails {
  nrPropertyId: number;
  slug: string;
  streetAddress: string;
  locationWaterFront: boolean;
  locationWaterview: boolean;
  neighborhoodId: number;
  latitude: number;
  longitude: number;
  distToTheHub: number;
  distToBeach: number;
  nrPmoId: number;

  bedrooms: Bedroom[];
  bathrooms: string;

  nrPropertyPics: PropertyPic[];
  nrPropertyVideos: PropertyVideo[];
  nrProperty3dTours: Property3dTour[];
  nrPropFloorPlans: PropertyFloorPlan[];
  headline: string;
  highlights: Nullable<string>;
  description: string;
  floorsDescription: string[];
  basementDescription: string | null;
  atticDescription: string | null;
  inclAttic: boolean;
  inclBasement: boolean;

  totalLivingArea: number;
  totalParking: number;
  totalFloors: number;
  totalBedrooms: number;
  totalCapacity: number;
  totalBathrooms: number;
  totalDiningrooms: number;
  totalKitchens: number;
  nrVerification: boolean;
  activeFlag: boolean;
  scamFlag: boolean;
  disputeFlag: boolean;
  parking: boolean;
  listingId: string;
  neighborhood: string;
  floors: FloorDescription[];

  rentalRates: RentalRatesCalendarData[];
  availableCalendar: AvailabilityCalendarData[];
  houseRules: HouseRules[];

  outdoorAmenities: OutdoorAmenity[];
  laundryAmenities: LaundryAmenity[];
  essentials: EssentialAmenity[];
  entertainment: EntertainmentAmenity[];
  kitchenAmenities: KicthenAmenity[];
  featuredAmenities: FeaturedAmenity[];

  averageNightlyRate?: number;
  avg_rating?: number;
  ratings_count?: number;

  cancellationPolicyId: Nullable<{
    description: string;
    id: number;
    name: string;
  }>;

  titleTag: string;
  metaDescription: string;
  metaRobots: string;
  metaCanonical: string;
  managedByOwner?: boolean;
}

export interface FloorDescription {
  floorId: number;
  floorTitle: string;
  floorDesc: string;
  nrPropertyId: number | null;
}

export interface EssentialAmenity {
  available: boolean;
  essentials: string;
  essentialsAmenityTypeId: number;
  iconIdentifier: string;
}

export interface EntertainmentAmenity {
  available: boolean;
  entertainmentAmenityType: string;
  entertainmentId: number;
  iconIdentifier: string;
}

export interface OutdoorAmenity {
  available: boolean;
  outdoorAmenityType: string;
  outdoorAmenityTypeId: number;
  iconIdentifier: string;
}

export interface KicthenAmenity {
  available: boolean;
  iconIdentifier: string;
  kitchenAmenityType: string;
  kitchenAmenityTypeId: number;
}

export interface LaundryAmenity {
  laundryAmenityTypeId: number;
  laundryAmenityType: string;
  available: boolean;
  iconIdentifier: string;
}

export type FeaturedAmenity = {
  amenity: string;
  iconIdentifier: string;
};

export interface HouseRules {
  activeFlag: boolean;
  created: string;
  houseRuleTypeId: number;
  modified: string;
  name: string;
  nrPropHouseRuleId: number;
  nrPropertyId: number;
  value: string;
}

export enum BlockedType {
  OWNER_TIME = 'owner',
  LEASED = 'leased',
  OTHER = 'other',
}

export interface AvailabilityCalendarData {
  nrPropAvailableCalendarId: number;
  nrAgentId: Nullable<any>;
  bookingSourceId: Nullable<any>;
  blockedType: BlockedType;
  blockedFrom: string;
  blockedTo: string;
  agreementFile: Nullable<string>;
  customSource: Nullable<string>;
  customAgent: Nullable<string>;
  rentAmount: Nullable<number>;
  renterName: Nullable<string>;
  created: string;
  modified: string;
  nrPropertyId: number;
  bookingId: Nullable<number>;
  note?: Nullable<string>;
}

export interface ListingReviews {
  id: number;
  name: string;
  email: Nullable<string>;
  rating: number;
  comment: string;
  created: string;
  modified: string;
  nrPropertyId: number;
}

export interface EnhancedEcomItem {
  item_name: string;
  item_id: number | string;
  price: number | string;
  item_brand: number | string;
  item_category: string;
  item_category2: string;
  item_category3: string;
  item_category4: string;
  item_list_name: string;
  index: number;
  quantity: string;
}

export enum PetType {
  DOG = 'Dog',
  CAT = 'Cat',
  OTHER = 'OTHER',
}
