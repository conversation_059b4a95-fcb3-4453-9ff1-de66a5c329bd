import { EntityID } from './common';

export enum UserType {
  OWNER = 0,
  MANAGER = 1,
  RENTER = 2,
}

export enum NR_USER_TYPES {
  ADMIN = 'ADMIN',
  MANAGER = 'MANAGER',
  PMO = 'PMO',
  RENTER = 'RENTER',
  AGENT = 'AGENT',
}

export type USERTYPE_PAYLOAD = {
  propertymanager?: boolean;
  propertyowner?: boolean;
  nrrenter?: boolean;
  nradminflag?: boolean;
  nrmanagerflag?: boolean;
  nragent?: boolean;
};

export type MyListingItem = {
  nrPropertyId: EntityID;
  streetAddress: string;
};

export type ProfileData = {
  nrRenterId?: EntityID;
  nrEmpId?: EntityID;
  nrPmoId?: number;
  nrAgentId?: EntityID;
  city?: string;
  company?: string;
  email1?: string;
  email2?: string;
  emailVerify?: boolean;
  idVerify?: boolean;
  firstname?: string;
  lastname?: string;
  nrProfileComplete?: boolean;
  nrUserId?: EntityID;
  nrVerifyFlag?: boolean;
  photo?: string;
  shortDescription?: string;
  state?: string;
  streetaddress1?: string;
  streetaddress2?: string;
  userDateofBirth?: string;
  userLoginEmail?: string;
  userLoginCellphone?: string;
  zipcode?: string;
  verification_status?: string | null;
  cellphone?: string;
  myProperties?: MyListingItem[];
  fullAccess?: boolean;
  associatedPmoId?: number;
};
