export enum SocialLoginType {
  GOOGLE = 'GOOGLE',
  FACEBOOK = 'FACEBOOK',
}

export enum AuthTabType {
  REGISTER = 'REGISTER',
  LOGIN = 'LOGIN',
}

export enum AuthenticationStep {
  PHONE_ENTRY = 'PHONE_ENTRY',
  VERIFICATION_CODE_ENTRY = 'VERIFICATION_CODE_ENTRY',
  REGISTER = 'REGISTER',
  LOGIN = 'LOGIN',
  VERIFY_EMAIL = 'VERIFY_EMAIL',
  VERIFY_EMAIL_MESSAGE = 'VERIFY_EMAIL_MESSAGE',
  ACCOUNT_EXISTS = 'ACCOUNT_EXISTS',
  THIRD_PARTY_VERIFY_LOGIN = 'THIRD_PARTY_VERIFY_LOGIN',
  THIRD_PARTY_VERIFY_REGISTER = 'THIRD_PARTY_VERIFY_REGISTER',
}
