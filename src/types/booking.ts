export interface BookingAvailabilityData {
  rent: number;
  occupancyTaxesFees: number;
  securityDeposit: number;
  total: number;
  headline: string;
  nantucket_fee?: number;
  occupancy_tax?: number;
  other_fees?: number;
  nr_property_id: number;
  street_address: string;
  image_url?: string;
  total_bedrooms?: number;
  total_capacity?: number;
  bathrooms?: string;
  pets_considered?: boolean;
  pet_fee?: number;
  average_nightly_rate?: number;
  description?: string;
  payment_schedule?: any[][];
  travel_insurance_amount?: number;
  charge_community_impact_fee?: boolean;
  rule_based_discount?: number;
  discount_type?: string;
  total_rent?: number;
}

export type FormattedBookingData = {
  occupancyTax: number;
  totalBeforeDiscount: number;
  totalWithoutTaxes: number;
  grandTotal: number;
  nantucketRentalsFee: number;
  averageNightlyRate: number;
};

export enum BookingPaymentMethod {
  CREDIT_CARD = 'credit_card',
  ACH = 'ach',
}
