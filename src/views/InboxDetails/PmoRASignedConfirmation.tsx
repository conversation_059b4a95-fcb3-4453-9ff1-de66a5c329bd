import React from 'react';

import SvgCsvUploaded from '@/common/assets/svgs/CsvUploaded';
import Button from '@/components/core/Button';
import { H3, P, P4 } from '@/components/core/Typography';
import { MESSAGES_ROUTE } from '@/constants/routes';

import { useRouter } from 'next/router';

const PmoRASignedConfirmation = () => {
  const { push } = useRouter();
  return (
    <div className="container py-[30px] sm:py-[40px] md:py-[100px]">
      <div className="text-center max-w-[846px] mx-auto">
        <div className="mb-7.5">
          <SvgCsvUploaded width={100} height={100} />
        </div>
        <div className="mb-5">
          <H3>Rental Agreement is Confirmed</H3>
        </div>
        <div className="mb-7.5">
          <P color="text.disabled">
            Your signed Rental Agreement is confirmed. The renter’s payment will be processed now
            and your payout will be sent as soon as funds clear.
          </P>
          <br />
          <P color="text.disabled">
            We use industry leader, <PERSON><PERSON>, to process incoming and outgoing payments. Payouts to
            Homeowners typically arrive 2-3 days after the lease is fully signed.
          </P>
          <br />
          <P color="text.disabled">
            If you have any questions about this Rental Agreement, please contact us.
          </P>
          <br />
          <P color="text.disabled">
            To communicate with the Renter about the upcoming stay, use the Messages tool.
          </P>
        </div>
        <div>
          <Button onClick={() => push(MESSAGES_ROUTE)} className="!my-5 !px-10 !py-3 bg-#15A5E5">
            <P4 color="common.white">View your Messages</P4>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PmoRASignedConfirmation;
