import React from 'react';

import { P } from '@/components/core/Typography';
import { NRProperties } from '@/redux/interfaces';
import { Card } from '@mui/material';

import Image from 'next/image';
import Link from 'next/link';

type Props = {
  property: NRProperties;
};

const FeaturedRentalItem = ({ property }: Props) => {
  return (
    <Link href={`/${property.slug}`} style={{ textDecoration: 'none' }} prefetch={true}>
      <Card
        sx={{
          paddingX: 1,
          paddingY: 2,
          display: 'flex',
        }}
      >
        <div>
          <Image
            alt="Listing image"
            src={property.NrPropertyPics[0].nrPropertyPicPath}
            width={48}
            height={48}
            style={{ objectFit: 'cover', objectPosition: 'center' }}
          />
        </div>
        <P sx={{ marginLeft: 2 }}>{property.headline}</P>
      </Card>
    </Link>
  );
};

export default FeaturedRentalItem;
