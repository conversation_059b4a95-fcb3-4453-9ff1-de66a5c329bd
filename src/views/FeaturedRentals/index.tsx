import React from 'react';

import { StyledPaginationItem } from '@/components/core/Pagination/styles';
import { H5 } from '@/components/core/Typography';
import { FEATURED_LISTINGS_ROUTE } from '@/constants/routes';
import { NRProperties, Pagination as PaginationInterface } from '@/redux/interfaces';
import { Container, Grid } from '@mui/material';

import dynamic from 'next/dynamic';
import Link from 'next/link';

import FeaturedRentalItem from './FeaturedRentalItem';

const Pagination = dynamic(() => import('@/components/core/Pagination'));

type Props = {
  listingsData: { results: NRProperties[] } & PaginationInterface;
};

const FeaturedListings = ({ listingsData }: Props) => {
  const { next, results } = listingsData;
  return (
    <Container sx={{ marginY: { xs: 4, md: 6 } }}>
      <H5 textAlign="center">Featured Nantucket Rental Homes</H5>
      <Grid container spacing={2} sx={{ marginY: { xs: 3, md: 4 } }}>
        {results.map((_result, index) => (
          <Grid key={index} item xs={12} sm={6} md={4}>
            <FeaturedRentalItem property={_result} />
          </Grid>
        ))}
      </Grid>
      {next && (
        <Pagination
          count={Math.ceil((listingsData?.count ?? 0) / 20)}
          page={listingsData?.currentPage ?? 1}
          renderItem={(item) => {
            if (item.type === 'end-ellipsis') {
              return <StyledPaginationItem {...item} />;
            }
            return (
              <Link href={`${FEATURED_LISTINGS_ROUTE}?page=${item.page}`} prefetch={true}>
                <StyledPaginationItem {...item} sx={{ borderRadius: 0.5 }} />
              </Link>
            );
          }}
        />
      )}
    </Container>
  );
};

export default FeaturedListings;
