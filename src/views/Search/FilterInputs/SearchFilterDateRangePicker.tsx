import React, { useContext, useMemo } from 'react';

import { PropertySearchContext } from '@/contexts/PropertySearchContext';
import { EntityID } from '@/redux/interfaces';

import { SEARCH_FILTERS, SearchFilterConfig } from '../searchFilterConfig';
import { CustomDateRangePickerWrapper } from '../styles';

import { SearchFilter } from '..';
import WeeksPicker, { formatDateRangeText, formatDateRangeValue } from './WeeksPicker';

type Props = {
  filter: SearchFilterConfig;
  filters: SearchFilter[];
  onSelectFilter: (filter: SearchFilter) => void;
  onEditFilter: (id: EntityID, newFilterData: SearchFilter) => void;
  handleClose?: () => void;
  landingFilter?: boolean;
};

const SearchFilterDateRangePicker = ({
  filter,
  filters,
  onSelectFilter,
  onEditFilter,
  handleClose,
  landingFilter,
}: Props) => {
  const { filters: allFilters } = useContext(PropertySearchContext);
  const selectedFilter = filters?.find((_f) => _f.key === filter.key) as any;
  const turnoverDay = useMemo(() => {
    const turnoverDayFilterValue = (allFilters?.find((_f) => _f.key === SEARCH_FILTERS.TURNOVER_DAY)
      ?.value as number[])?.[0];
    return turnoverDayFilterValue === 7 ? 0 : turnoverDayFilterValue ?? 0;
  }, [allFilters]);

  const onConfirm = (highlightedWeek: Date[]) => {
    const firstHighlightedDay = highlightedWeek[0];
    const lastHighlightedDay = highlightedWeek[highlightedWeek.length - 1];
    if (selectedFilter) {
      onEditFilter(selectedFilter.id, {
        ...selectedFilter,
        text: formatDateRangeText(firstHighlightedDay, lastHighlightedDay),
        value: formatDateRangeValue(firstHighlightedDay, lastHighlightedDay),
      });
      if (handleClose) {
        handleClose();
      }
      return;
    }
    onSelectFilter({
      id: filter.id,
      key: SEARCH_FILTERS.DATE_RANGE,
      text: formatDateRangeText(firstHighlightedDay, lastHighlightedDay),
      value: formatDateRangeValue(firstHighlightedDay, lastHighlightedDay),
      color: filter.color,
    });
  };

  return (
    <CustomDateRangePickerWrapper
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      minHeight={landingFilter ? { xs: '400px', md: 'initial' } : {}}
      sx={{
        width: '100%',
        paddingY: 2,
      }}
    >
      <WeeksPicker
        filter={filter}
        filters={filters}
        onSelectFilter={onSelectFilter}
        onEditFilter={onEditFilter}
        handleClose={handleClose}
        onConfirm={onConfirm}
        turnoverDay={turnoverDay}
      />
    </CustomDateRangePickerWrapper>
  );
};

export default SearchFilterDateRangePicker;
