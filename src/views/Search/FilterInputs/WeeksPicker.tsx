import React, { startTransition, useContext, useEffect, useMemo, useState } from 'react';

import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

import Button from '@/components/core/Button';
import { PropertySearchContext } from '@/contexts/PropertySearchContext';
import { EntityID } from '@/redux/interfaces';
import { getWeekDaysForDate } from '@/utils/datepicker';
import { Box, Divider, useMediaQuery, useTheme } from '@mui/material';

import { eachDayOfInterval, startOfDay, isAfter, isSameDay, addDays, getDay } from 'date-fns';
import dayjs from 'dayjs';

import { SEARCH_FILTERS, SearchFilterConfig } from '../searchFilterConfig';

import { SearchFilter } from '..';

type Props = {
  filter: SearchFilterConfig;
  filters: SearchFilter[];
  onSelectFilter: (filter: SearchFilter) => void;
  onEditFilter: (id: EntityID, newFilterData: SearchFilter) => void;
  handleClose?: () => void;
  onConfirm: (highlightDates: Date[]) => void;
  turnoverDay?: number;
};

export const formatDateRangeText = (startDateValue: Date, endDateValue: Date) => {
  const startDate = dayjs(startDateValue).format('ddd MMM D');
  const endDate = dayjs(endDateValue).format('ddd MMM D, YYYY');
  return `${startDate} - ${endDate}`;
};

export const formatDateRangeValue = (startDateValue: Date, endDateValue: Date) => {
  const startDate = dayjs(startDateValue).format('YYYY-MM-DD');
  const endDate = dayjs(endDateValue).format('YYYY-MM-DD');
  return [startDate, endDate];
};

const WeeksPicker = ({
  onConfirm,
  filter,
  filters,
  onSelectFilter,
  onEditFilter,
  handleClose,
  turnoverDay,
}: Props) => {
  const { filters: allFilters } = useContext(PropertySearchContext);

  const theme = useTheme();
  const isMobileScreen = useMediaQuery(theme.breakpoints.down('md'));
  const [selectedDate, setSelectedDate] = useState<any | null>(null);
  const [highlightedWeek, setHighlightedWeek] = useState<Date[]>([]);
  const [initialHighlighted, setInitialHighlighted] = useState<Date[]>([]);
  const [excludedDates, setExcludedDates] = useState<Date[] | []>([]);
  const selectedFilter = filters?.find((_f) => _f.key === filter.key) as any;

  const numberOfWeeks = useMemo(
    () =>
      (allFilters?.find((_f) => _f.key === SEARCH_FILTERS.WEEKS_NUMBERS)?.value as any[])?.[0] ?? 1,
    [allFilters],
  );

  useEffect(() => {
    if (selectedFilter) {
      const weekStart = dayjs(selectedFilter.value[0]).toDate();
      const weekEnd = dayjs(selectedFilter.value[1]).toDate();
      const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });
      setInitialHighlighted(weekDays);
      startTransition(() => setHighlightedWeek(weekDays));
      setSelectedDate(weekStart);
    }
  }, [selectedFilter]);

  const handleDayMouseEnter = (date: Date) => {
    const weekDays = getWeekDaysForDate(date, numberOfWeeks);
    const now = startOfDay(new Date());
    const filteredWeekDays = weekDays.filter((day) => isAfter(day, now) || isSameDay(day, now));
    const includesTurnoverDay = filteredWeekDays.some((day) => getDay(day) === turnoverDay);

    if (includesTurnoverDay) {
      const filteredTurnoverDay = filteredWeekDays.find(
        (day) => getDay(day) === turnoverDay,
      ) as Date;
      const nextTurnoverDay = addDays(filteredTurnoverDay, 7 * numberOfWeeks);
      const weekDaysFromTurnAroundDays = eachDayOfInterval({
        start: filteredTurnoverDay,
        end: nextTurnoverDay,
      });
      setExcludedDates([]);
      startTransition(() => setHighlightedWeek(weekDaysFromTurnAroundDays));
    } else {
      setExcludedDates(filteredWeekDays);
      startTransition(() => setHighlightedWeek([]));
    }
    setSelectedDate(null);
  };

  const handleDesktopDateChange = (date: any) => {
    const selectedWeek = highlightedWeek.find((day) => dayjs(day).isSame(date, 'day'));
    setSelectedDate(selectedWeek);
    const weekDays = getWeekDaysForDate(date, numberOfWeeks);

    const now = startOfDay(new Date());
    const filteredWeekDays = weekDays.filter((day) => isAfter(day, now) || isSameDay(day, now));
    const filteredTurnoverDay = filteredWeekDays.find((day) => getDay(day) === turnoverDay) as Date;
    const nextTurnoverDay = addDays(filteredTurnoverDay, 7 * numberOfWeeks);
    const weekDaysFromTurnAroundDays = eachDayOfInterval({
      start: filteredTurnoverDay,
      end: nextTurnoverDay,
    });
    setExcludedDates([]);
    startTransition(() => setHighlightedWeek(weekDaysFromTurnAroundDays));
    setInitialHighlighted(weekDaysFromTurnAroundDays);
    const firstHighlightedDay = weekDaysFromTurnAroundDays[0];
    const lastHighlightedDay = weekDaysFromTurnAroundDays[weekDaysFromTurnAroundDays.length - 1];
    if (selectedFilter) {
      onEditFilter(selectedFilter.id, {
        ...selectedFilter,
        text: formatDateRangeText(firstHighlightedDay, lastHighlightedDay),
        value: formatDateRangeValue(firstHighlightedDay, lastHighlightedDay),
      });
      if (handleClose) {
        handleClose();
      }
      return;
    }
    onSelectFilter({
      id: filter.id,
      key: SEARCH_FILTERS.DATE_RANGE,
      text: formatDateRangeText(firstHighlightedDay, lastHighlightedDay),
      value: formatDateRangeValue(firstHighlightedDay, lastHighlightedDay),
      color: filter.color,
    });
  };

  const handleMobileDateChange = (date: any) => {
    setSelectedDate(null);
    const weekDays = getWeekDaysForDate(date, numberOfWeeks);
    const now = startOfDay(new Date());
    const filteredWeekDays = weekDays.filter((day) => isAfter(day, now) || isSameDay(day, now));
    const includesTurnoverDay = filteredWeekDays.some((day) => getDay(day) === turnoverDay);
    if (includesTurnoverDay) {
      const filteredTurnoverDay = filteredWeekDays.find(
        (day) => getDay(day) === turnoverDay,
      ) as Date;
      const nextTurnoverDay = addDays(filteredTurnoverDay, 7 * numberOfWeeks);
      const weekDaysFromTurnAroundDays = eachDayOfInterval({
        start: filteredTurnoverDay,
        end: nextTurnoverDay,
      });
      startTransition(() => setHighlightedWeek(weekDaysFromTurnAroundDays));
      setInitialHighlighted(weekDaysFromTurnAroundDays);
    } else {
      startTransition(() => setHighlightedWeek([]));
    }
  };

  const handleMonthMouseLeave = () => {
    startTransition(() => setHighlightedWeek(initialHighlighted));
  };

  return (
    <>
      <DatePicker
        selected={selectedDate}
        onDayMouseEnter={handleDayMouseEnter}
        selectsStart
        onChange={isMobileScreen ? handleMobileDateChange : handleDesktopDateChange}
        inline
        minDate={new Date()}
        excludeDates={excludedDates}
        monthsShown={isMobileScreen ? 1 : 2}
        highlightDates={highlightedWeek}
        onMonthMouseLeave={handleMonthMouseLeave}
        calendarClassName="CustomDateRangePicker"
        dayClassName={() => 'CustomDateRangePicker__Day'}
        weekDayClassName={() => 'CustomDateRangePicker__Weekday'}
        formatWeekDay={(day: string) => day.slice(0, 1)}
      />
      {isMobileScreen && (
        <Box display="flex" alignSelf="flex-end" flexDirection="column" width="100%">
          <Divider />
          <Box display="flex" justifyContent="flex-end" mx={2} my={1}>
            <Button onClick={() => onConfirm(highlightedWeek)}>Confirm</Button>
          </Box>
        </Box>
      )}
    </>
  );
};

export default WeeksPicker;
