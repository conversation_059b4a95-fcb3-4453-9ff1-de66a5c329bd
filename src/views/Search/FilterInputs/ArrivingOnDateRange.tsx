import React, { useContext, useMemo } from 'react';

import LinkButton from '@/components/core/Button/LinkButton';
import RadioGroupComponent from '@/components/core/Input/RadioGroup';
import { PropertySearchContext } from '@/contexts/PropertySearchContext';
import { EntityID } from '@/redux/interfaces';
import { Box, Divider } from '@mui/material';

import config, { SEARCH_FILTERS, SearchFilterConfig } from '../searchFilterConfig';
import { CustomDateRangePickerWrapper } from '../styles';

import { SearchFilter } from '..';
import WeeksPicker, { formatDateRangeText, formatDateRangeValue } from './WeeksPicker';

type Props = {
  filter: SearchFilterConfig;
  filters: SearchFilter[];
  onSelectFilter: (filter: SearchFilter, keepOpen?: boolean) => void;
  onEditFilter: (id: EntityID, newFilterData: SearchFilter) => void;
  handleClose?: () => void;
  landingFilter?: boolean;
  onLessThanWeek?: () => void;
};

const ArrivingOnDateRange = ({
  onSelectFilter,
  onEditFilter,
  handleClose,
  onLessThanWeek,
}: Props) => {
  const { filters: allFilters } = useContext(PropertySearchContext);
  const selectedDateRangeFilter = useMemo(
    () => allFilters?.find((_f) => _f.key === SEARCH_FILTERS.DATE_RANGE) as any,
    [allFilters],
  );
  const turnoverDaySelectedFilter = useMemo(
    () => allFilters?.find((_f) => _f.key === SEARCH_FILTERS.TURNOVER_DAY) as any,
    [allFilters],
  );

  const onSelectTurnaroundDay = (event: React.ChangeEvent<HTMLInputElement>) => {
    event.stopPropagation();
    const turnoverDay = Number(event.target.value);
    const isSaturday = turnoverDay === 6;

    if (turnoverDaySelectedFilter) {
      onEditFilter(turnoverDaySelectedFilter.id, {
        ...turnoverDaySelectedFilter,
        text: isSaturday ? 'Check-in Day: Saturday' : 'Check-in Day: Sunday',
        value: [turnoverDay, turnoverDay],
        unit: isSaturday ? 'saturday' : 'sunday',
      });
    } else {
      onSelectFilter(
        {
          id: '11',
          key: SEARCH_FILTERS.TURNOVER_DAY,
          text: isSaturday ? 'Check-in Day: Saturday' : 'Check-in Day: Sunday',
          value: [turnoverDay, turnoverDay],
          unit: isSaturday ? 'saturday' : 'sunday',
        },
        true,
      );
    }
  };

  const onConfirm = (highlightedWeek: Date[]) => {
    const firstHighlightedDay = highlightedWeek[0];
    const lastHighlightedDay = highlightedWeek[highlightedWeek.length - 1];
    if (selectedDateRangeFilter) {
      onEditFilter(selectedDateRangeFilter.id, {
        ...selectedDateRangeFilter,
        text: formatDateRangeText(firstHighlightedDay, lastHighlightedDay),
        value: formatDateRangeValue(firstHighlightedDay, lastHighlightedDay),
      });
      if (handleClose) {
        handleClose();
      }
      return;
    }
    onSelectFilter({
      id: '3',
      key: SEARCH_FILTERS.DATE_RANGE,
      text: formatDateRangeText(firstHighlightedDay, lastHighlightedDay),
      value: formatDateRangeValue(firstHighlightedDay, lastHighlightedDay),
    });
  };

  return (
    <CustomDateRangePickerWrapper
      display="flex"
      flexDirection="column"
      justifyContent="space-between"
      sx={{
        width: '100%',
      }}
    >
      <Box paddingX={2} paddingY={1} mb={1}>
        <RadioGroupComponent
          label="Arriving on"
          value={String(turnoverDaySelectedFilter?.value?.[0] ?? 6)}
          options={[
            { label: 'Saturday', value: '6' },
            { label: 'Sunday', value: '0' },
          ]}
          onChange={onSelectTurnaroundDay}
        />
      </Box>
      <Divider />
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="space-between"
        minHeight={{ xs: '396px', md: 'initial' }}
      >
        <WeeksPicker
          filter={config[2]}
          filters={allFilters.filter((_f) => _f?.key === SEARCH_FILTERS.DATE_RANGE)}
          onSelectFilter={onSelectFilter}
          onEditFilter={onEditFilter}
          handleClose={handleClose}
          onConfirm={onConfirm}
          turnoverDay={turnoverDaySelectedFilter?.value?.[0] ?? 6}
        />
      </Box>
      <LinkButton style={{ marginTop: 8, marginBottom: 8 }} onClick={onLessThanWeek}>
        Other day or less than a week?
      </LinkButton>
    </CustomDateRangePickerWrapper>
  );
};

export default ArrivingOnDateRange;
