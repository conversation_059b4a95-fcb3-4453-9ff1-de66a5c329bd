import { useContext, useEffect } from 'react';

import { useDispatch, useSelector } from 'react-redux';

import { PropertySearchContext } from '@/contexts/PropertySearchContext';
import { actionGetNeighborhoodData } from '@/redux/actions/dataManagement';
import { actionGetSearchAmenities } from '@/redux/actions/propertiesSearch';
import { MainState, ProgressStatus } from '@/redux/interfaces';
import { useMediaQuery, useTheme } from '@mui/material';

import Filters from './Filters';
import { ApplyButton, ClearFiltersButton } from './styles';

type Props = {
  onSearch: () => void;
};

const SearchDialogComponent = ({ onSearch }: Props) => {
  const dispatch = useDispatch();
  const theme = useTheme();
  const isMobileScreen = useMediaQuery(theme.breakpoints.down('sm'));
  const { onClearFilters } = useContext(PropertySearchContext);
  const { progressStatus } = useSelector((state: MainState) => state.propertiesSearch);

  useEffect(() => {
    dispatch(actionGetNeighborhoodData());
    dispatch(actionGetSearchAmenities());
  }, [dispatch]);

  return (
    <div className="relative">
      <div className="mb-8">
        <Filters />
      </div>
      <div className={`flex gap-2 ${isMobileScreen ? 'flex-col-reverse' : 'row'}`}>
        <div className="w-full md:w-6/12">
          <ClearFiltersButton
            fullWidth
            sx={{ fontSize: '16px', lineHeight: '24px' }}
            size="large"
            onClick={onClearFilters}
          >
            Clear Filters
          </ClearFiltersButton>
        </div>
        <div className="w-full md:w-6/12">
          <ApplyButton
            fullWidth
            size="large"
            type="submit"
            sx={{ fontSize: '16px', lineHeight: '24px' }}
            disabled={progressStatus === ProgressStatus.LOADING}
            onClick={onSearch}
          >
            Apply
          </ApplyButton>
        </div>
      </div>
    </div>
  );
};

export default SearchDialogComponent;
