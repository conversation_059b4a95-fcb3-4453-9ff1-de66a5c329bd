import React from 'react';

import { useDispatch } from 'react-redux';

import SuccessIcon from '@/common/assets/svgs/Success';
import LinkButton from '@/components/core/Button/LinkButton';
import { H4 } from '@/components/core/Typography';
import { actionResetState } from '@/redux/reducers/property-listing';
import Box from '@mui/material/Box';

import { GrayTitle, Note } from './styles';

const Success = () => {
  const dispatch = useDispatch();
  return (
    <Box p={2} textAlign="center">
      <SuccessIcon />
      <H4>Congratulations!!!</H4>
      <H4>Your property profile is complete!</H4>
      <Box mt={2}>
        <GrayTitle>Nantucket Rentals will contact you soon to verify your information.</GrayTitle>
        <GrayTitle>
          To enlist more properties,{' '}
          <LinkButton onClick={() => dispatch(actionResetState())}>click here</LinkButton>.
        </GrayTitle>
      </Box>
      <Box mt={1}>
        <Note>
          You can also enlist properties anytime by clicking Add New Properties on the taskbar or
          from your account dashboard.
        </Note>
        <Note>Please feel free to reach out to Nantucket Rentals for any assistance!</Note>
      </Box>
    </Box>
  );
};

export default Success;
