import dayjs from 'dayjs';
import * as Yup from 'yup';
import { AnyObject } from 'yup/lib/types';

const checkRequired = (value: (string | null | undefined)[] | undefined) =>
  !value?.some((_el) => _el == null);

export const geCheckoutValidationSchema = () =>
  Yup.object().shape({
    dateRange: Yup.array()
      .of(Yup.string().nullable())
      .test('validate', 'Invalid Range.', (value) => {
        const required = checkRequired(value);
        if (!required) {
          return true;
        }
        return !value?.some((_v) => !dayjs(_v).isValid());
      })
      .test('required', 'Please select dates.', checkRequired),
  });

export const getGuestValuesSchema = (capacity: number) => {
  return Yup.object().shape({
    adults: Yup.number()
      .typeError('Please select the number of adults.')
      .required('Adults is required')
      .min(1, 'At least one adult is required for booking'),
    children: Yup.number()
      .typeError('Please select the number of children.')
      .required('Children is required')
      .when(
        'adults',
        (
          adults: number,
          schema: Yup.NumberSchema<number | undefined, AnyObject, number | undefined>,
        ) => {
          return schema.max(capacity - adults, `Maximum guests for this property is ${capacity}`);
        },
      ),
  });
};
