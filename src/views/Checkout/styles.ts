import { colors } from '@/common/style/theme';
import { H5 } from '@/components/core/Typography';
import Box from '@mui/material/Box';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import { withStyles } from '@mui/styles';

import styled, { css } from 'styled-components';

interface AddRemoveContainerProps {
  selected?: boolean;
  disabled?: boolean;
}

export const CheckoutForm = styled(Box)`
  background: #f5f9ff;
  border: 1px solid #dee1eb;
  border-radius: 4px;
`;

export const CheckoutField = styled(Box)`
  background: ${({ theme }) => theme.colors.common.white};
`;

export const ControlledSelection = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 2px 8px 2px;
  background: ${colors.white};
  box-sizing: border-box;
  flex: 1;
`;
export const AddRemoveContainer = styled(Box)`
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  background: ${colors.white};
  width: 104px;
`;

const disabledStyles = css<AddRemoveContainerProps>`
  opacity: 0.2 !important;
  cursor: not-allowed !important;
  border: 1px solid ${colors.text.disabled} !important;
`;

export const IconContainer = styled.button<AddRemoveContainerProps>`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2px;
  border: 1px solid ${colors.text.disabled};
  border-radius: 100%;
  cursor:pointer;
  background-color: ${({ disabled }: AddRemoveContainerProps) => (disabled ? '' : colors.white)};
  ${({ disabled }) => disabled && disabledStyles}

  &:hover {
    border: 1px solid ${colors.black};
  }
};

`;

export const InfoButton = styled(IconButton)`
  color: ${({ theme }) => theme.colors.common.black};
`;

export const TaxTooltip = withStyles((theme) => ({
  tooltip: {
    backgroundColor: theme.colors.common.black,
    padding: 20,
    fontFamily: 'Poppins',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: 12,
    lineHeight: '150%',
    minWidth: 348,
  },
}))(Tooltip);

export const StyledH5 = styled(H5)`
  color: ${colors.grey.main};
`;
