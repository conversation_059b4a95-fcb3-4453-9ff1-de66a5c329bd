'use client';

import { ReactNode, useCallback } from 'react';

import { useIntercom } from 'react-use-intercom';

type Props = {
  children: ReactNode;
  className?: string;
};

const IntercomWrapper = ({ children, className = '' }: Props) => {
  const { show } = useIntercom();
  const onClick = useCallback(() => {
    show();
  }, [show]);

  return (
    <div onClick={onClick} onKeyDown={onClick} role="button" tabIndex={0} className={className}>
      {children}
    </div>
  );
};

export default IntercomWrapper;
