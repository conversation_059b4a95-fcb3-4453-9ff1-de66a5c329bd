'use client';

import { memo } from 'react';

import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

const ProgressDialog = ({ open, title }: { open: boolean; title?: string }) => {
  return (
    <Dialog open={open}>
      <DialogContent
        className={cn('h-min p-2 md:p-4', 'rounded-xl shadow-md bg-white dark:bg-zinc-900')}
        hideCloseButton
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        {title && <p className="text-sm mb-2 text-zinc-700 dark:text-zinc-300 mt-0">{title}</p>}
        <div className="w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded-full overflow-hidden">
          <div className="h-full bg-blue-500 animate-pulse w-1/2 rounded-full" />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default memo(ProgressDialog);
