'use client';

import { memo, useEffect, useRef } from 'react';

type Props = {
  playListUrl: string;
};

const BackgroundVideo = ({ playListUrl }: Props) => {
  const videoRef = useRef<HTMLVideoElement | null>(null);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    if (video.canPlayType('application/vnd.apple.mpegurl')) {
      video.src = playListUrl;
    } else {
      import('hls.js').then(({ default: Hls }) => {
        if (Hls.isSupported()) {
          const hls = new Hls();
          hls.loadSource(playListUrl);
          hls.attachMedia(video);
        }
      });
    }
  }, [playListUrl]);

  return (
    <div className="shadow-bg-video relative h-full w-full hidden md:block md:z-[1]">
      <video
        ref={videoRef}
        autoPlay
        loop
        muted
        playsInline
        poster="/images/list-your-home-bg.avif"
        preload="auto"
        className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full h-full object-cover"
      >
        <source src={playListUrl} type="application/vnd.apple.mpegurl" />
      </video>
      <div className="absolute inset-0 video-bg-overlay" />
    </div>
  );
};

export default memo(BackgroundVideo);
