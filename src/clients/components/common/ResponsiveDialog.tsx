'use client';

import { memo, ReactNode, useEffect, useState } from 'react';

import { useMediaQuery } from 'react-responsive';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogTitle,
} from '@/components/ui/dialog';

import SlideUpPane from './SlideUpPane';

type ResponsiveDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  children: ReactNode;
  hideCloseButton?: boolean;
  dialogClassName?: string;
  drawerClassName?: string;
  drawerContentClassName?: string;
  preventOutsideInteraction?: boolean;
  mobileBreakpoint?: number;
  description?: string;
};

/**
 * A responsive dialog component that renders as a modal dialog on desktop
 * and as a bottom drawer on mobile devices.
 */
const ResponsiveDialog = ({
  open,
  onOpenChange,
  children,
  hideCloseButton = false,
  dialogClassName = '',
  drawerClassName = '',
  drawerContentClassName = '',
  preventOutsideInteraction = false,
  mobileBreakpoint = 768,
  description,
}: ResponsiveDialogProps) => {
  const isMobile = useMediaQuery({ maxWidth: mobileBreakpoint });
  const [mounted, setMounted] = useState(false);

  // Handle SSR - ensure we only render the correct component after mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't render anything during SSR to prevent hydration mismatch
  if (!mounted) return null;

  // For mobile devices, use a drawer
  if (isMobile) {
    return (
      <SlideUpPane isShowing={open} wrapperClassName={drawerClassName}>
        <div className={`p-4 flex-1 overflow-auto pb-10 ${drawerContentClassName}`}>{children}</div>
      </SlideUpPane>
    );
  }

  // For desktop, use a dialog
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogOverlay className="bg-[rgba(217,217,217,0.90)]" />
      <DialogContent
        className={`p-0 md:min-w-[220px] ${dialogClassName}`}
        hideCloseButton={hideCloseButton}
        onInteractOutside={preventOutsideInteraction ? (e) => e.preventDefault() : undefined}
        aria-describedby={description ? undefined : 'responsive-dialog-description'}
      >
        <DialogTitle className="hidden" />
        <DialogDescription id="responsive-dialog-description" className="hidden">
          {description || 'Dialog content'}
        </DialogDescription>
        {children}
      </DialogContent>
    </Dialog>
  );
};

export default memo(ResponsiveDialog);
