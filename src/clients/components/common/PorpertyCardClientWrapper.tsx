'use client';

import { memo, ReactNode, useCallback } from 'react';

import { NRProperties } from '@/types/properties';
import {
  formatEnhanceEcommerceProperty,
  pushEcommerceDataLayer,
} from '@/utils/enhancedEcomAnanalytics';
import { formatProperty } from '@/utils/getSegment';

type Props = { children: ReactNode; propertyDetails: NRProperties };

const PropertyCardClientWrapper = ({ children, propertyDetails }: Props) => {
  const onClickCard = useCallback(() => {
    pushEcommerceDataLayer(
      'select_item',
      [formatEnhanceEcommerceProperty(formatProperty(propertyDetails as any))],
      true,
    );
  }, [propertyDetails]);

  return (
    <div
      onClick={onClickCard}
      onKeyDown={onClickCard}
      role="button"
      tabIndex={0}
      className="w-full"
    >
      {children}
    </div>
  );
};

export default memo(PropertyCardClientWrapper);
