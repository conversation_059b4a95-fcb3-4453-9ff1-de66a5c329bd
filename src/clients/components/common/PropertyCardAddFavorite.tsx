'use client';

import { memo, MouseEvent, useCallback, useState } from 'react';

import { HeartIcon as HeartIconSolid } from '@heroicons/react/20/solid';
import { HeartIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

import { useLoginDialog, useProfile } from '@/contexts/selectors/app-context-selectors';
import { togglePropertyFavorite } from '@/services/client-side/properties';

type Props = {
  isFavorite?: boolean;
  propertyId: number;
};

const PropertyCardAddFavorite = ({ isFavorite, propertyId }: Props) => {
  const [isFav, setIsFav] = useState<boolean>(!!isFavorite);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { onToggleLoginDialog } = useLoginDialog();
  const { profileData } = useProfile();

  const onClickFav = useCallback(
    (e: MouseEvent<HTMLDivElement>) => {
      if (profileData) {
        setIsLoading(true);
        togglePropertyFavorite(propertyId)
          .then((_d) => {
            console.log('added', _d);
            setIsFav(!isFav);
            setIsLoading(false);
            if (isFav) {
              toast.error('Removed from favorites');
            } else {
              toast.success('Added to favorites');
            }
          })
          .catch((e) => {
            console.error('Failed', e);
            toast.error(e);
            setIsLoading(false);
          });
      } else {
        onToggleLoginDialog();
      }
      e.stopPropagation();
      e.preventDefault();
    },
    [isFav, onToggleLoginDialog, profileData, propertyId],
  );
  return (
    <div
      data-prevent-nprogress={true}
      onClick={onClickFav}
      role="presentation"
      className="w-9 h-9 bg-white shadow-lg rounded-full flex items-center justify-center"
    >
      {isLoading ? (
        <div className="loading loading-spinner loading-xs text-metal-gray" />
      ) : isFav ? (
        <HeartIconSolid className="w-4 h-4 text-[#FF4141]" />
      ) : (
        <HeartIcon className="w-4 h-4 text-primary-text" />
      )}
    </div>
  );
};

export default memo(PropertyCardAddFavorite);
