'use client';

import { memo, useCallback, useState } from 'react';

import Button from '@/clients/ui/Button';

type Props = {
  text: string;
  className?: string;
};

const ExpandableText = memo(({ text, className = '' }: Props) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = useCallback(() => {
    setIsExpanded((prev) => !prev);
  }, []);

  const shouldShowToggle = text.length > 275;
  const displayedText = isExpanded ? text : text.slice(0, 275);

  return (
    <div>
      <p className={className}>
        {displayedText}

        {shouldShowToggle && (
          <span
            role="presentation"
            onClick={toggleExpanded}
            className="text-carolina-blue underline cursor-pointer font-semibold pl-1"
          >
            {isExpanded ? 'Show Less' : 'Show More'}
          </span>
        )}
      </p>
    </div>
  );
});

export default ExpandableText;
