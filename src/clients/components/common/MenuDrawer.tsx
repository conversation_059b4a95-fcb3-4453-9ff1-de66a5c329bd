'use client';

import { useCallback, useState } from 'react';

import { Bars3Icon, ChevronRightIcon, XMarkIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';
import { Skeleton } from '@/components/ui/skeleton';
import { useLoginDialog, useProfile } from '@/contexts/selectors/app-context-selectors';

import classNames from 'classnames';
import dynamic from 'next/dynamic';
import Link from 'next/link';
import { usePathname, useSearchParams } from 'next/navigation';
import { Drawer } from 'vaul';

const MobileUserMenuDialog = dynamic(() => import('./UserMenu/MobileUserMenuDialog'), {
  loading: () => <Skeleton className="w-[80vw] h-[346px] rounded-[30px]" />,
});

export default function MenuDrawer() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { onToggleLoginDialog } = useLoginDialog();
  const { profileData } = useProfile();
  const [open, setOpen] = useState<boolean>(false);
  const [openUserMenu, setOpenUserMenu] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setOpen(false);
  }, []);

  const onToggleUserMenu = useCallback(() => {
    setOpenUserMenu((prev) => !prev);
  }, []);

  const onLogin = useCallback(() => {
    onToggleLoginDialog();
    setTimeout(() => setOpen(false), 300);
  }, [onToggleLoginDialog]);

  return (
    <>
      <Drawer.Root direction="bottom" open={open} onOpenChange={setOpen}>
        <Drawer.Trigger
          className={classNames(
            'lg:hidden absolute right-4 top-[60px] bg-black/5 border border-solid border-platinium border-opacity-50 w-[48px] h-[48px] rounded-full flex items-center justify-center z-10',
            {
              'border-white text-white':
                pathname === '/' ||
                pathname === '/list-your-home' ||
                (pathname === '/property-search' && searchParams?.get('view') === 'map'),
              hidden:
                open ||
                (pathname === '/property-search' && searchParams?.get('view') !== 'map') ||
                (pathname?.includes('request-to-book') && !pathname.includes('approval')) ||
                pathname?.includes('campaign'),
            },
          )}
        >
          <Bars3Icon className="w-6 h-6" />
        </Drawer.Trigger>
        <Drawer.Portal>
          <Drawer.Content
            className="bg-black/40 fixed inset-0 z-30 backdrop-blur"
            aria-describedby="Menu Drawer"
          >
            <Drawer.Title className="hidden"></Drawer.Title>
            <Drawer.Description className="hidden"></Drawer.Description>
            <div
              className="absolute right-6 top-5 bg-transparent border border-solid border-white border-opacity-50 w-[48px] h-[48px] rounded-full flex items-center justify-center"
              role="presentation"
              onClick={onClose}
            >
              <XMarkIcon className="w-6 h-6 text-white" />
            </div>
            <div className="absolute left-[10vw] right-[10vw] top-1/2 -translate-y-1/2 bg-[rgba(222,225,235,0.05)] border border-[#DEE1EB] border-solid backdrop-blur-xl rounded-[30px] p-1.5 flex flex-col gap-y-4">
              <Link
                href="https://nantucketrentals.com/the-island/"
                onClick={onClose}
                className="p-4 text-center font-medium no-underline text-white"
              >
                The Island
              </Link>
              <Link
                href="https://nantucketrentals.com/things-to-do/"
                onClick={onClose}
                className="p-4 text-center font-medium no-underline text-white"
              >
                Things To Do
              </Link>
              <Link
                href="https://nantucketrentals.com/where-to-go/"
                onClick={onClose}
                className="p-4 text-center font-medium no-underline text-white"
              >
                Where To Go
              </Link>
              <Link
                href="https://nantucketrentals.com/our-picks"
                onClick={onClose}
                className="p-4 text-center font-medium no-underline text-white"
              >
                Our Picks
              </Link>
              <Link
                href="/list-your-home"
                onClick={onClose}
                className="p-4 text-center font-medium no-underline text-white"
              >
                Owners
              </Link>
              {profileData ? (
                <Button
                  className="w-full !bg-carolina-blue text-white rounded-[100px] p-4 text-center relative"
                  intent="ghost"
                  onClick={onToggleUserMenu}
                >
                  {profileData.firstname} {profileData.lastname}
                  <ChevronRightIcon className="w-4 h-4 absolute right-4 top-1/2 -translate-y-1/2" />
                </Button>
              ) : (
                <Button
                  className="px-4 py-4 text-center font-medium no-underline text-primary-text rounded-[100px] border-0"
                  onClick={onLogin}
                >
                  Login
                </Button>
              )}
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
      {openUserMenu && <MobileUserMenuDialog open={openUserMenu} onToggle={onToggleUserMenu} />}
    </>
  );
}
