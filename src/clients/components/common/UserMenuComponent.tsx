'use client';

import { useCallback, useEffect, useState } from 'react';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Skeleton } from '@/components/ui/skeleton';
import {
  useIsFetchingProfile,
  useProfile,
  useUserType,
} from '@/contexts/selectors/app-context-selectors';
import { NR_USER_TYPES, ProfileData } from '@/types/profile';

import dynamic from 'next/dynamic';

const UserMenu = dynamic(() => import('./UserMenu'), {
  loading: () => <Skeleton className="w-[200px] h-[286px]" />,
});

type Props = {
  children: React.ReactNode;
  userData: ProfileData;
  userType: NR_USER_TYPES;
};

const UserMenuComponent = ({ children, userData, userType }: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const { isFetchingProfile, setIsFetchingProfile } = useIsFetchingProfile();
  const { setProfileData } = useProfile();
  const { setUserType } = useUserType();

  const onToggle = useCallback(() => setOpen((prev) => !prev), []);

  useEffect(() => {
    if (userData) {
      setIsFetchingProfile(false);
      setProfileData(userData);
    }
  }, [setIsFetchingProfile, setProfileData, userData]);

  useEffect(() => {
    if (userType) {
      setUserType(userType);
    }
  }, [setUserType, userType]);

  return (
    <>
      {userData ? (
        <Popover open={open} onOpenChange={setOpen}>
          <PopoverTrigger asChild>{children}</PopoverTrigger>
          <PopoverContent
            className="w-screen h-screen md:w-max md:h-auto border border-solid border-english-manor border-opacity-40 p-5 md:mt-2.5"
            align="end"
          >
            <UserMenu onClose={onToggle} />
          </PopoverContent>
        </Popover>
      ) : isFetchingProfile ? (
        <Skeleton className="h-12 w-12 rounded-full bg-grey-main" />
      ) : (
        children
      )}
    </>
  );
};

export default UserMenuComponent;
