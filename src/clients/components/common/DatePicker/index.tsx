'use client';

import { Dispatch, SetStateAction, useCallback, useEffect, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import { Input } from '@/components/ui/input';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import { format, isValid, parse } from 'date-fns';
import { twMerge } from 'tailwind-merge';

import CustomDatePicker from './CustomDatePicker';

type Props = {
  className?: string;
  placeholder?: string;
  value?: any;
  popOverClassName?: string;
  selected?: Date;
  onSelect: Dispatch<SetStateAction<Date | undefined>>;
  dateFormat?: string;
};

const DatePicker = ({
  className = '',
  placeholder = 'Select date',
  popOverClassName,
  selected,
  onSelect,
  dateFormat = 'MM-dd-yyyy',
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [inputValue, setInputValue] = useState<string>('');
  const [inputFocused, setInputFocused] = useState<boolean>(false);

  // Update input value when selected date changes
  useEffect(() => {
    if (selected && isValid(selected)) {
      setInputValue(format(selected, dateFormat));
    } else {
      setInputValue('');
    }
  }, [selected, dateFormat]);

  const formatDateInput = useCallback((value: string): string => {
    // Remove all non-digits
    const digitsOnly = value.replace(/\D/g, '');

    // Format based on the number of digits
    if (digitsOnly.length <= 2) {
      return digitsOnly;
    } else if (digitsOnly.length <= 4) {
      return `${digitsOnly.slice(0, 2)}-${digitsOnly.slice(2)}`;
    } else {
      return `${digitsOnly.slice(0, 2)}-${digitsOnly.slice(2, 4)}-${digitsOnly.slice(4, 8)}`;
    }
  }, []);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const rawValue = e.target.value;

      // Only allow digits and hyphens
      if (!/^[\d-]*$/.test(rawValue)) {
        return;
      }

      // Format the input value
      const formattedValue = formatDateInput(rawValue);
      setInputValue(formattedValue);

      // Try to parse the input as a date
      if (formattedValue.length >= 10) {
        // MM-dd-yyyy format has 10 characters
        try {
          const parsedDate = parse(formattedValue, dateFormat, new Date());
          if (isValid(parsedDate)) {
            onSelect(parsedDate);
          }
        } catch (error) {
          // Invalid date format, do nothing
        }
      }
    },
    [dateFormat, onSelect, formatDateInput],
  );

  const handleInputFocus = useCallback(() => {
    setInputFocused(true);
  }, []);

  const handleInputBlur = useCallback(() => {
    setInputFocused(false);

    // If input is empty, clear the date
    if (!inputValue.trim()) {
      onSelect(undefined);
      return;
    }

    // If input doesn't match a valid date, revert to the selected date format
    if (selected && isValid(selected)) {
      setInputValue(format(selected, dateFormat));
    } else {
      setInputValue('');
    }
  }, [inputValue, selected, dateFormat, onSelect]);

  const onClickedTrigger = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
  }, []);

  const handleCalendarIconClick = useCallback(() => {
    setOpen(true);
  }, []);

  const onSelectDate = useCallback(
    (date?: Date) => {
      onSelect(date);
      setOpen(false);
    },
    [onSelect],
  );

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild onClick={onClickedTrigger}>
          <div className="relative">
            <Input
              value={inputValue}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onBlur={handleInputBlur}
              placeholder={placeholder}
              className={twMerge(
                'pr-10 border border-solid border-platinium py-[8.5px] px-[14px] text-sm text-metal-gray rounded-sm cursor-text',
                className,
              )}
              pattern="\d*"
              inputMode="numeric"
              maxLength={10} // MM-dd-yyyy format has 10 characters
            />
            <div
              className="absolute right-3 top-1/2 -translate-y-1/2 cursor-pointer"
              onClick={handleCalendarIconClick}
            >
              <ChevronDownIcon className="w-4 h-4 text-platinum" />
            </div>
          </div>
        </PopoverTrigger>

        {open && (
          <PopoverContent
            className={twMerge(
              'w-max border border-solid border-english-manor border-opacity-40 p-6 z-[9999]',
              popOverClassName,
            )}
            align="center"
          >
            <CustomDatePicker selected={selected} onSelect={onSelectDate} />
          </PopoverContent>
        )}
      </Popover>
    </div>
  );
};

export default DatePicker;
