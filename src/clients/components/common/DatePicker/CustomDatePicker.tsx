import React, { useMemo, useState, useCallback } from 'react';

import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

const getDaysArray = (year: number, month: number) => {
  const firstDay = new Date(year, month, 1).getDay();
  const totalDays = new Date(year, month + 1, 0).getDate();

  return Array.from({ length: firstDay + totalDays }, (_, i) =>
    i < firstDay ? null : i - firstDay + 1,
  );
};

const DayButton = React.memo(
  ({
    day,
    isToday,
    isSelected,
    onDayClick,
  }: {
    day: number | null;
    isToday: boolean;
    isSelected: boolean;
    onDayClick: (day: number | null) => void;
  }) => {
    const handleClick = useCallback(() => {
      onDayClick(day);
    }, [onDayClick, day]);

    return (
      <Button
        variant="ghost"
        disabled={!day}
        onClick={handleClick}
        className={`
          cursor-pointer h-10 w-10 p-0 aria-selected:opacity-100 rounded-full border border-transparent border-solid hover:border-[#6D7380] hover:rounded-full font-medium text-sm
          ${!day ? 'invisible' : ''}
          ${isToday ? 'border border-blue-500' : ''}
          ${isSelected ? 'bg-blue-500 text-white' : 'hover:bg-blue-100'}
        `}
      >
        {day}
      </Button>
    );
  },
);

type Props = {
  selected?: Date;
  onSelect: (date?: Date) => void;
};

export default function CustomDatePicker({ selected, onSelect }: Props) {
  // Initialize with selected date's month/year if available, otherwise current date
  const initialDate = selected || new Date();
  const [month, setMonth] = useState(initialDate.getMonth());
  const [year, setYear] = useState(initialDate.getFullYear());

  const days = useMemo(() => getDaysArray(year, month), [year, month]);

  const years = React.useMemo(
    () => Array.from({ length: new Date().getFullYear() + 10 - 1900 + 1 }, (_, i) => 1900 + i),
    [],
  );

  const isToday = useCallback(
    (day: number) => {
      const now = new Date();
      return day === now.getDate() && month === now.getMonth() && year === now.getFullYear();
    },
    [month, year],
  );

  const isSelected = useCallback(
    (day: number) =>
      selected?.getDate() === day &&
      selected?.getMonth() === month &&
      selected?.getFullYear() === year,
    [selected, month, year],
  );

  const handleDayClick = useCallback(
    (day: number | null) => {
      if (day) {
        onSelect(new Date(year, month, day));
      }
    },
    [month, onSelect, year],
  );

  const handleYearChange = useCallback((e: string) => {
    setYear(+e);
  }, []);

  const handleMonthChange = useCallback((e: string) => {
    setMonth(+e);
  }, []);

  const handleNext = useCallback(() => {
    setMonth((prevMonth) => {
      if (prevMonth === 11) {
        setYear((prevYear) => prevYear + 1);
        return 0;
      }
      return prevMonth + 1;
    });
  }, []);

  const handlePrev = useCallback(() => {
    setMonth((prevMonth) => {
      if (prevMonth === 0) {
        setYear((prevYear) => prevYear - 1);
        return 11;
      }
      return prevMonth - 1;
    });
  }, []);

  // If selected date changes externally (from input), update the calendar view
  React.useEffect(() => {
    if (selected) {
      setMonth(selected.getMonth());
      setYear(selected.getFullYear());
    }
  }, [selected]);

  return (
    <div className="p-4 bg-white shadow-md rounded-md w-fit mx-auto">
      <div className="flex items-center justify-center gap-x-2 mb-2">
        <Button variant="ghost" size="icon" onClick={handlePrev} className="cursor-pointer">
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <Select onValueChange={handleMonthChange} value={month.toString()}>
          <SelectTrigger className="w-[110px] border-none cursor-pointer">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {Array.from({ length: 12 }).map((_, i) => (
              <SelectItem key={i} value={i.toString()}>
                {new Date(year, i).toLocaleString('default', { month: 'long' })}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select onValueChange={handleYearChange} value={year.toString()}>
          <SelectTrigger className="w-[90px] border-none cursor-pointer">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {years.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Button variant="ghost" size="icon" onClick={handleNext} className="cursor-pointer">
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex">
        {weekDays.map((d) => (
          <div
            key={d}
            className="text-muted-foreground rounded-md w-9 h-10 font-normal text-[0.8rem] flex items-center justify-center flex-1"
          >
            {d[0]}
          </div>
        ))}
      </div>

      <div className="grid grid-cols-7 text-center">
        {days.map((day, idx) => (
          <DayButton
            key={idx}
            day={day}
            isToday={day ? isToday(day) : false}
            isSelected={day ? isSelected(day) : false}
            onDayClick={handleDayClick}
          />
        ))}
      </div>
    </div>
  );
}
