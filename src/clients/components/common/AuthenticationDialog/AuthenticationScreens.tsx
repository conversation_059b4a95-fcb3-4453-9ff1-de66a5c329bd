'use client';

import { useCallback, useState } from 'react';

import { AuthenticationStep } from '@/types/login';
import { Nullable } from '@/utils/common';

import ConfirmNumberForm from './ConfirmNumberForm';
import LoginForm from './LoginForm';
import PhoneEntryForm from './PhoneEntryForm';
import RegisterForm from './RegisterForm';
import ThirdPartyVerifyForm from './ThirdPartyVerifyForm';
import VerifyEmail from './VerifyEmail';
import VerifyEmailMessage from './VerifyEmailMessage';

type Props = {
  onToggle: () => void;
  defaultPhone?: Nullable<string>;
  defaultUserid?: Nullable<number>;
  defaultAuthStep?: AuthenticationStep;
};

const AuthenticationScreens = ({
  onToggle,
  defaultPhone,
  defaultUserid,
  defaultAuthStep,
}: Props) => {
  const [authStep, setAuthStep] = useState<AuthenticationStep>(
    defaultAuthStep ?? AuthenticationStep.PHONE_ENTRY,
  );
  const [phone, setPhone] = useState<Nullable<string>>(defaultPhone ?? null);
  const [email, setEmail] = useState<Nullable<string>>(null);
  const [userId, setUserId] = useState<Nullable<number>>(defaultUserid ?? null);

  const onBack = useCallback(() => {
    if (defaultAuthStep) {
      onToggle();
    } else {
      setAuthStep(AuthenticationStep.PHONE_ENTRY);
    }
  }, [defaultAuthStep, onToggle]);

  return (
    <>
      {authStep === AuthenticationStep.PHONE_ENTRY && (
        <PhoneEntryForm
          setAuthStep={setAuthStep}
          onToggle={onToggle}
          setPhone={setPhone}
          setUserId={setUserId}
        />
      )}
      {authStep === AuthenticationStep.VERIFICATION_CODE_ENTRY && phone && userId && (
        <ConfirmNumberForm
          setAuthStep={setAuthStep}
          onToggle={onToggle}
          userId={userId}
          phone={phone}
          onBack={onBack}
        />
      )}
      {(authStep === AuthenticationStep.THIRD_PARTY_VERIFY_REGISTER ||
        authStep === AuthenticationStep.THIRD_PARTY_VERIFY_LOGIN) && (
        <ThirdPartyVerifyForm authStep={authStep} setAuthStep={setAuthStep} onToggle={onToggle} />
      )}
      {authStep === AuthenticationStep.LOGIN && (
        <LoginForm setAuthStep={setAuthStep} onToggle={onToggle} />
      )}
      {authStep === AuthenticationStep.REGISTER && userId && (
        <RegisterForm
          setAuthStep={setAuthStep}
          onToggle={onToggle}
          setEmail={setEmail}
          userId={userId}
          phone={phone ?? ''}
          onBack={onBack}
        />
      )}
      {authStep === AuthenticationStep.VERIFY_EMAIL && userId && (
        <VerifyEmail
          setAuthStep={setAuthStep}
          onToggle={onToggle}
          phone={phone ?? ''}
          email={email ?? ''}
          userId={userId}
          onBack={onBack}
        />
      )}
      {authStep === AuthenticationStep.VERIFY_EMAIL_MESSAGE && (
        <VerifyEmailMessage onToggle={onToggle} />
      )}
    </>
  );
};

export default AuthenticationScreens;
