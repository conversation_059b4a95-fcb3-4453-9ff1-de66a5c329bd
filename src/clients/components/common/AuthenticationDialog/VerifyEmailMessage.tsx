'use client';

import { Cross2Icon } from '@radix-ui/react-icons';

import { P1 } from '@/app/ui/common/Typography';
import Button from '@/clients/ui/Button';
import EmailIcon from '@/common/assets/svgs/Email';
import { Separator } from '@/components/ui/separator';

type Props = {
  onToggle: () => void;
};

const VerifyEmailMessage = ({ onToggle }: Props) => {
  return (
    <div className="text-center w-full pt-safe-top pb-safe-bottom">
      <div className="md:px-[30px] py-4 relative text-center font-medium text-lg md:text-2xl leading-[140%] tracking-[0.5px] text-carolina-blue">
        Verify Your Email to Login
        <Button className="absolute right-0 md:right-[30px] !p-0" intent="ghost" onClick={onToggle}>
          <Cross2Icon className="h-5 w-5" />
        </Button>
      </div>
      <Separator />
      <div className="w-full pt-5 md:pt-6 md:px-[30px] pb-0 md:pb-[30px] max-h-[90dvh] overflow-y-auto">
        <h5 className="m-0 text-base leading-6 font-medium">
          Please verify your email address for Nantucket Rentals sign up:
        </h5>
        <EmailIcon className="my-6" />
        <P1 className="m-0 mt-2 text-grey-main">
          We sent you an email. Please check your email and click the link.
        </P1>
      </div>
    </div>
  );
};

export default VerifyEmailMessage;
