'use client';

import { useCallback, useEffect, useState } from 'react';

import { useMediaQuery } from 'react-responsive';

import AuthenticationDialog from '@/clients/components/common/AuthenticationDialog';
import AuthenticationScreens from '@/clients/components/common/AuthenticationDialog/AuthenticationScreens';
import SlideUpPane from '@/clients/components/common/SlideUpPane';
import { AuthenticationStep } from '@/types/login';
import { Nullable } from '@/utils/common';

type Props = {
  open: boolean;
  onToggle: () => void;
  defaultPhone?: Nullable<string>;
  defaultUserid?: Nullable<number>;
  defaultAuthStep?: AuthenticationStep;
};

const LoginDialogWrapper = ({
  open,
  onToggle,
  defaultPhone,
  defaultUserid,
  defaultAuthStep,
}: Props) => {
  const isMobile = useMediaQuery({ maxWidth: 768 });
  const [mounted, setMounted] = useState(false);

  // Handle SSR - ensure we only render the correct component after mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleToggle = useCallback(() => {
    onToggle();
  }, [onToggle]);

  // Don't render anything during SSR to prevent hydration mismatch
  if (!mounted) return null;

  // For mobile devices, use a slide up pane
  if (isMobile) {
    return (
      <>
        <SlideUpPane isShowing={open}>
          <div className="p-4 flex-1 overflow-auto pb-10">
            <AuthenticationScreens
              onToggle={onToggle}
              defaultPhone={defaultPhone}
              defaultUserid={defaultUserid}
              defaultAuthStep={defaultAuthStep}
            />
          </div>
        </SlideUpPane>
      </>
    );
  }

  // For desktop, use a dialog
  return (
    <AuthenticationDialog
      open={open}
      onToggle={handleToggle}
      defaultPhone={defaultPhone}
      defaultUserid={defaultUserid}
      defaultAuthStep={defaultAuthStep}
    />
  );
};

export default LoginDialogWrapper;
