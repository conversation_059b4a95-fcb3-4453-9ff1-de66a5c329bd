'use client';

import { FormEvent, useCallback, useState } from 'react';

import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';

import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/Button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { useIsFetchingProfile } from '@/contexts/selectors/app-context-selectors';
import { actionRequestOTP, actionVerifyOTP } from '@/services/client-side/registration';
import { ProgressStatus } from '@/types/common';
import { AuthenticationStep } from '@/types/login';
import FormHelperText from '@/ui/atoms/FormHelperText';

type Props = {
  setAuthStep: (_step: AuthenticationStep) => void;
  phone: string;
  userId: number;
  onToggle: () => void;
  onBack: () => void;
};

const ConfirmNumberForm = ({ setAuthStep, phone, userId, onToggle, onBack }: Props) => {
  const [error, setError] = useState<string>('');
  const [otp, setOTP] = useState<string>('');
  const { setIsFetchingProfile } = useIsFetchingProfile();
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);

  const onChangeOTP = useCallback((event: FormEvent<HTMLInputElement>) => {
    setOTP(event.currentTarget.value);
  }, []);

  const onVerify = useCallback(() => {
    if (!otp) {
      setError('OTP is required');
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    setError('');

    actionVerifyOTP({ user_id: userId, otp: Number(otp), phone_number: phone })
      .then(({ data, msg }: any) => {
        console.log('OTP success data is', data);
        if (data?.userNotFound) {
          setAuthStep(AuthenticationStep.REGISTER);
        } else if (msg === 'Invalid otp') {
          throw msg;
        } else {
          revalidateTagByName(`user-profile`);
          setIsFetchingProfile(true);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          onToggle();
        }
      })
      .catch((err) => {
        setProgressStatus(ProgressStatus.FAILED);
        console.log('error is', err);
        toast(err);
        setError('Invalid OTP');
      });
  }, [onToggle, otp, phone, setAuthStep, setIsFetchingProfile, userId]);

  const onClickResend = useCallback(() => {
    actionRequestOTP({
      phone_number: phone,
      user_id: userId,
      signup_flow: true,
    })
      .then((data) => {
        console.debug('the data is', data);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        toast.success('OTP sent');
      })
      .catch((err) => {
        console.debug('error is', err);
        setError(err);
        setProgressStatus(ProgressStatus.FAILED);
      });
    setOTP('');
    setError('');
  }, [phone, userId]);
  return (
    <div className="w-full pt-safe-top pb-safe-bottom">
      <div className="md:px-[30px] py-4 relative text-center font-medium text-lg md:text-2xl leading-[140%] tracking-[0.5px] flex items-center justify-center">
        <Button className="absolute left-0 md:left-[30px] !p-0" intent="ghost" onClick={onBack}>
          <ArrowLeftIcon className="h-5 w-5" />
        </Button>
        Confirm your number
      </div>
      <Separator />
      <div className="w-full pt-5 md:pt-6 md:px-[30px] pb-0 md:pb-[30px]">
        <Input
          value={otp}
          onChange={onChangeOTP}
          wrapperclassName="w-[160px] m-auto"
          className="w-full px-4 py-2 rounded-[32px] text-center"
          placeholder="- - - - - -"
          pattern="\d*"
          inputMode="numeric"
          autoComplete="off"
        />
        {error && (
          <FormHelperText error className="text-center">
            {error}
          </FormHelperText>
        )}
        <Button
          isLoading={progressStatus === ProgressStatus.LOADING}
          onClick={onVerify}
          className="text-sm py-4 md:py-4 px-5 md:px-9 font-medium bg-carolina-blue text-white flex m-auto mt-6 w-full rounded-[32px]"
        >
          Continue
        </Button>
        <Button
          intent="ghost"
          onClick={onClickResend}
          className="cursor-pointer text-center text-metal-gray px-5 md:px-9 py-2 text-sm underline mt-2 w-full"
        >
          Resend OTP
        </Button>
      </div>
    </div>
  );
};

export default ConfirmNumberForm;
