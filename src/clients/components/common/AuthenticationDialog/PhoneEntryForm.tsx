'use client';

import { Dispatch, SetStateAction, useCallback, useState } from 'react';

import { Cross2Icon } from '@radix-ui/react-icons';
import { useGoogleLogin } from '@react-oauth/google';

import { revalidateTagByName } from '@/app/actions/revalidateTag';
import Button from '@/clients/ui/Button';
import SvgEnvelopeIcon from '@/common/assets/svgs/Envelope';
import SvgGoogleIcon from '@/common/assets/svgs/GoogleLogin';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import useForm from '@/hooks/useForm';
import { actionGoogleLogin } from '@/services/client-side/registration';
import { generateOTP } from '@/services/server/login';
import { Nullable, ProgressStatus } from '@/types/common';
import { AuthenticationStep } from '@/types/login';
import { getRandomNumberWithDigits } from '@/utils/common';

type LoginFormValues = {
  phone: string;
};

type Props = {
  setAuthStep: (_step: AuthenticationStep) => void;
  onToggle: () => void;
  setPhone: Dispatch<SetStateAction<Nullable<string>>>;
  setUserId: Dispatch<SetStateAction<Nullable<number>>>;
};

const PhoneEntryForm = ({ setAuthStep, onToggle, setPhone, setUserId }: Props) => {
  const [err, setErr] = useState<any>(null);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const { formState, errors, onChange, preSubmitCheck: preSubmitCheck } = useForm<LoginFormValues>(
    {
      phone: '',
    },
    {
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Phone is required.`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
    },
    false,
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const handleGoogleLogin = useGoogleLogin({
    onSuccess: (tokenResponse) => {
      setErr(null);

      actionGoogleLogin({
        access_token: tokenResponse.access_token,
      })
        .then((data) => {
          console.debug('the data is', data);
          revalidateTagByName(`user-profile`);
          setProgressStatus(ProgressStatus.SUCCESSFUL);
          onToggle();
        })
        .catch((err) => {
          console.debug('error is', err);
          setErr(err);
          setProgressStatus(ProgressStatus.FAILED);
        });
    },
    onError: (error) => {
      console.log('Login Failed', error);
      setErr(error);
    },
  });

  const handleContinueEmail = useCallback(() => {
    setAuthStep(AuthenticationStep.LOGIN);
  }, [setAuthStep]);

  const onContinue = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    const user_id = getRandomNumberWithDigits(5);
    generateOTP({
      phone_number: `+1${formState.phone}`,
      user_id,
      signup_flow: true,
    })
      .then((data) => {
        console.debug('the data is', data);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
        setAuthStep(AuthenticationStep.VERIFICATION_CODE_ENTRY);
        setPhone(`+1${formState.phone}`);
        setUserId(user_id);
      })
      .catch((err) => {
        console.debug('error is', err);
        setErr(err);
        setProgressStatus(ProgressStatus.FAILED);
      });
  }, [formState.phone, preSubmitCheck, setAuthStep, setPhone, setUserId]);

  return (
    <div className="w-full pt-safe-top pb-safe-bottom">
      <div className="md:px-[30px] py-4 relative text-center font-medium text-lg md:text-2xl leading-[140%] tracking-[0.5px] flex items-center justify-center">
        Login or sign up
        <Button className="absolute right-0 md:right-[30px] !p-0" intent="ghost" onClick={onToggle}>
          <Cross2Icon className="h-5 w-5" />
        </Button>
      </div>
      <Separator />
      <div className="w-full pt-5 md:pt-6 md:px-[30px] md:pb-[30px]">
        <p className="m-0 text-lg text-center">Welcome to Nantucket Rentals</p>
        {err && (
          <div>
            {Object.keys(err).map((_key, index) => (
              <p key={index} className="m-0 mb-2 text-error">
                {_key} : <span className="font-medium">{err[_key]}</span>
              </p>
            ))}
          </div>
        )}
        <Input
          type="phone"
          name="phone"
          value={formState.phone}
          placeholder="Phone Number"
          className="w-full mt-5 px-5 md:px-9 py-4 rounded-[32px]"
          pattern="\d*"
          inputMode="numeric"
          onChange={onChangeTextInput}
          helperText={errors?.phone ?? ''}
          error={!!errors?.phone?.length}
        />
        <p className="text-center text-[8px] m-0 text-primary-text leading-[150%] mt-2">
          We&apos;ll text you to confirm your number. Standard message and date rates may apply.
        </p>
        {/* {!!errors?.phone?.length && (
          <div className="text-center">
            <FormHelperText error>{errors?.phone ?? ''}</FormHelperText>
          </div>
        )} */}
        <Button
          className="font-medium w-full mt-5 rounded-[32px] px-5 md:px-9 py-4"
          onClick={onContinue}
          isLoading={progressStatus === ProgressStatus.LOADING}
        >
          Continue
        </Button>
        <div className="mt-6 mb-5 relative h-5 flex items-center justify-center">
          <Separator />
          <span className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 text-xs bg-white px-4 text-metal-gray font-medium">
            or
          </span>
        </div>
        <Button
          intent="outline"
          className="w-full rounded-[32px] px-5 md:px-9 py-4 text-primary-text font-medium leading-[24px] capitalize my-4 border-[1.5px] border-solid border-disabled"
          onClick={() => handleGoogleLogin()}
        >
          <SvgGoogleIcon className="mr-4" />
          Continue With Google
        </Button>
        <Button
          intent="outline"
          className="w-full rounded-[32px] px-5 md:px-9 py-4 text-primary-text font-medium leading-[24px] capitalize mb-4 border-[1.5px] border-solid border-disabled"
          onClick={handleContinueEmail}
        >
          <SvgEnvelopeIcon className="mr-4" />
          Continue With Email
        </Button>
        <p className="text-center text-[8px] m-0 text-primary-text leading-[150%]">
          Nantucket Rentals may send you marketing messages. You may opt out at anytime.
        </p>
      </div>
    </div>
  );
};

export default PhoneEntryForm;
