'use client';

import { memo, useMemo } from 'react';

import {
  ChevronLeftIcon,
  ChevronRightIcon,
  EllipsisHorizontalIcon,
} from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';

type PaginationProps = {
  page: number;
  onChangePage: (page: number) => void;
  pageCount: number;
};

const ClientSidePagination = ({ page, onChangePage, pageCount }: PaginationProps) => {
  const [left, right] = useMemo(() => {
    if (pageCount >= 7) {
      if (page > 5 && page < pageCount - 4) {
        return [Number(page) - 2, Number(page) + 2];
      } else if (page <= 5) {
        return [1, 7];
      } else {
        return [pageCount - 6, pageCount];
      }
    } else {
      return [0, 0];
    }
  }, [page, pageCount]);

  const shouldShowLeftEllipsis = useMemo(() => {
    if (pageCount <= 7) {
      return false;
    }
    return page > 5;
  }, [page, pageCount]);

  const shouldShowRightEllipsis = useMemo(() => {
    if (pageCount <= 7) {
      return false;
    }
    return left !== pageCount && right !== pageCount;
  }, [left, pageCount, right]);

  const ltOneToLast = page - 1 < pageCount;
  const isLast = page === pageCount;

  return (
    <div className="my-10 flex flex-wrap gap-1.5">
      <Button
        intent="outline"
        className="flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-disabled border-solid text-metal-gray no-underline text-sm !p-0"
      >
        <ChevronLeftIcon className="w-4 h-4" />
      </Button>
      {page > 5 && (
        <Button
          intent="outline"
          onClick={() => onChangePage(1)}
          className="flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-disabled border-solid text-metal-gray no-underline text-sm !p-0"
        >
          1
        </Button>
      )}
      {shouldShowLeftEllipsis && (
        <span className="flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-disabled border-solid">
          <EllipsisHorizontalIcon className="w-4 h-4" />
        </span>
      )}
      {Array(pageCount)
        .fill(null)
        .map((x, i) => {
          if (left === 0 && right === 0) i++;
          if ((i >= left && i < right) || (left === 0 && right === 0)) {
            const className = page == i ? 'bg-carolina-blue text-white' : 'border-disabled';
            return (
              <Button
                intent={page === i ? 'primary' : 'outline'}
                key={i}
                onClick={() => onChangePage(i)}
                className={`flex h-9 w-9 cursor-pointer items-center justify-center rounded text-metal-gray no-underline text-sm  border border-solid ${className}`}
              >
                <span className="text-center font-medium text-inherit">{i}</span>
              </Button>
            );
          }
        })}
      {shouldShowRightEllipsis && (
        <span className="flex h-9 w-9 cursor-pointer items-center justify-center rounded border border-disabled border-solid">
          <EllipsisHorizontalIcon className="w-4 h-4" />
        </span>
      )}
      {page > 4 && ltOneToLast && (
        <Button
          intent={page === pageCount ? 'primary' : 'outline'}
          onClick={() => onChangePage(pageCount)}
          className={`flex h-9 w-9 items-center justify-center rounded text-metal-gray no-underline text-sm border border-solid ${
            isLast ? 'bg-carolina-blue text-white' : 'border-disabled '
          }`}
        >
          <span className="text-center font-medium text-inherit">{pageCount}</span>
        </Button>
      )}
      <Button
        intent="outline"
        className="flex h-9 w-9 cursor-pointer items-center justify-center rounded text-metal-gray no-underline text-sm border border-disabled border-solid !p-0"
      >
        <ChevronRightIcon className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default memo(ClientSidePagination);
