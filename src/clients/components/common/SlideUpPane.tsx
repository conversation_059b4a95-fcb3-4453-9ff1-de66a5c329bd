'use client';

import { Fragment, ReactNode, useEffect, useRef } from 'react';

import { Transition } from '@headlessui/react';

import { twMerge } from 'tailwind-merge';

type Props = {
  isShowing?: boolean;
  children: ReactNode;
  wrapperClassName?: string;
};

const SlideUpPane = ({ isShowing, children, wrapperClassName }: Props) => {
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const body = document.getElementsByTagName('body')[0];

    // Clear any existing timeout
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    if (isShowing) {
      body.classList.add('requires-no-scroll');
    } else {
      // Use setTimeout to ensure the animation completes before removing the class
      timerRef.current = setTimeout(() => {
        body.classList.remove('requires-no-scroll');
        timerRef.current = null;
      }, 300); // Increase duration to ensure animation completes
    }

    // Cleanup on unmount
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      // Ensure class is removed when component unmounts
      body.classList.remove('requires-no-scroll');
    };
  }, [isShowing]);

  return (
    <>
      {/* Overlay */}
      <Transition
        as={Fragment}
        show={isShowing}
        enter="transition-opacity ease-linear duration-150"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity ease-linear duration-150"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="fixed inset-0 bg-black/40 z-50" />
      </Transition>

      {/* Slide Pane */}
      <Transition
        as={Fragment}
        show={isShowing}
        appear={true}
        enter="transition ease-linear duration-150 transform"
        enterFrom="translate-y-full"
        enterTo="translate-y-0"
        leave="transition ease-linear duration-150 transform"
        leaveFrom="translate-y-0"
        leaveTo="translate-y-full"
      >
        {(ref) => (
          <div
            ref={(el: HTMLDivElement) => {
              if (ref && el) {
                // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                // @ts-ignore - Headless UI's ref has a different type than React's ref
                ref.current = el;
              }
            }}
            id="slidePane"
            className={twMerge(
              'z-[55] bg-white fixed md:absolute inset-0 bottom-0 top-auto max-h-[90dvh] h-fit overflow-y-auto rounded-t-[10px]',
              wrapperClassName,
            )}
          >
            {children}
          </div>
        )}
      </Transition>
    </>
  );
};

export default SlideUpPane;
