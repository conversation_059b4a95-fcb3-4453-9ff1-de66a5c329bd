'use client';

import debounce from 'lodash/debounce';
import { useCallback, useEffect, useState } from 'react';

import { Input } from '@/components/ui/input';
import { Nullable } from '@/types/common';

import AutocompleteItems from './AutocompleteItems';

export type AutocompleteOption = { label: string; value: any };

type Props = {
  value?: Nullable<string>;
  options?: AutocompleteOption[];
  onSelect?: (_option: AutocompleteOption) => void;
  onChangeValue?: (text: string) => void;
  className?: string;
  isFetchingData?: boolean;
  fetchData?: (query?: string) => void;
  placeholder?: string;
};

const Autocomplete = ({
  options = [],
  onSelect,
  value,
  onChangeValue,
  className = '',
  isFetchingData,
  fetchData,
  placeholder = '',
}: Props) => {
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [query, setQuery] = useState('');

  const fetchDataDebounced = debounce((val = '') => {
    fetchData?.(val);
  }, 300);

  const onChangeQuery = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      setQuery(value);
      onChangeValue?.(value);
      fetchDataDebounced(value);
      if (!showPopup) {
        setShowPopup(true);
      }
    },
    [onChangeValue, fetchDataDebounced, showPopup],
  );

  const onClickOutside = useCallback(() => {
    if (query !== value) {
      if (query.length === 0) {
        onChangeValue?.('');
      } else if (value && value.length > 0 && options.length > 0) {
        setQuery(value);
      } else if (query.length > 0) {
        onChangeValue?.(query);
      }
    }
    setShowPopup(false);
  }, [options.length, onChangeValue, query, value]);

  useEffect(() => {
    fetchData?.();
  }, [fetchData]);

  useEffect(() => {
    if (value) {
      setQuery(value);
    }
  }, [value]);

  useEffect(() => {
    setActiveIndex(0);
  }, [options]);

  useEffect(() => {
    return () => {
      fetchDataDebounced.cancel();
    };
  }, [fetchDataDebounced]);

  return (
    <>
      <div className="relative w-full px-5 py-4 md:py-4 md:px-[36px] rounded-[32px] border border-[rgba(0,0,0,0.20)] [&:has(#autocompleListWrapper)]:rounded-b-none transition-all">
        <Input
          id="autocompleteInput"
          className={`px-2.5 py-[14px] w-full text-sm ${className}`}
          value={query}
          onChange={onChangeQuery}
          placeholder={placeholder}
          onFocus={() => {
            if (query.length === 0) {
              setShowPopup(true);
              setActiveIndex(0);
            }
          }}
        />
        {showPopup && options.length > 0 && (
          <AutocompleteItems
            options={options}
            activeIndex={activeIndex}
            setActiveIndex={setActiveIndex}
            onSelect={onSelect}
            onClosePopup={() => setShowPopup(false)}
            isFetchingData={isFetchingData}
          />
        )}
      </div>
      {showPopup && <div className="drawer-overlay absolute inset-0" onClick={onClickOutside} />}
    </>
  );
};

export default Autocomplete;
