'use client';

import { memo, useMemo, useRef } from 'react';

import { AutocompleteOption } from '.';

type Props = {
  options?: AutocompleteOption[];
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  onSelect?: (_o: AutocompleteOption) => void;
  onClosePopup: () => void;
  isFetchingData?: boolean;
};

const AutocompleteItems = ({
  options = [],
  activeIndex,
  setActiveIndex,
  onSelect,
  onClosePopup,
  isFetchingData,
}: Props) => {
  const selectRef = useRef<null | HTMLUListElement>(null);
  const activeListItemRef = useRef<null | HTMLLIElement>(null);

  const renderedOptions = useMemo(
    () =>
      options.map((_option, index) => (
        <li
          ref={index === activeIndex ? activeListItemRef : null}
          className={`px-5 md:px-[36px] py-1 cursor-pointer hover:bg-carolina-blue hover:text-white ${
            index === activeIndex ? 'bg-primary-slate activeIndex' : 'hover:bg-primary-slate'
          }`}
          onClick={() => {
            onSelect?.(_option);
            onClosePopup();
          }}
          key={index}
        >
          {_option.label}
        </li>
      )),
    [options, activeIndex, onSelect, onClosePopup],
  );

  return (
    <ul
      id="autocompleListWrapper"
      className="w-[calc(100%+2px)] absolute z-[99999] top-[50px] left-[-1px] right-[-1px] border border-[rgba(0,0,0,0.20)] rounded-b-[32px] bg-white max-h-[200px] overflow-y-auto pb-5"
      ref={selectRef}
    >
      {renderedOptions}
    </ul>
  );
};

export default memo(AutocompleteItems);
