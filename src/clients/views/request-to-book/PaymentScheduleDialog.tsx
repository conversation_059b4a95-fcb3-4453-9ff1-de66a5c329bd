'use client';

import { memo } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';

import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import Button from '@/clients/ui/Button';

type Props = {
  open: boolean;
  onToggle: () => void;
};

const PaymentScheduleDialog = memo(({ open, onToggle }: Props) => {
  return (
    <ResponsiveDialog
      open={open}
      onOpenChange={onToggle}
      dialogClassName="p-4 w-[80vw] max-w-md mx-auto h-min rounded-xl"
      drawerContentClassName="pb-4"
    >
      <div className="relative">
        <Button onClick={onToggle} intent="ghost" className="!p-0 absolute top-0 right-0 md:hidden">
          <XCircleIcon className="w-6 h-6" />
        </Button>

        <h3 className="font-medium m-0">Pay part now, part later</h3>
        <div className="text-sm text-gray-800 mt-2">
          You can pay for part of this reservation now, and the rest later. No additional fees.
        </div>
        <div className="text-sm font-medium mt-4">Pay part of the total now</div>
        <div className="text-sm text-gray-800">
          Confirm your reservation by paying a portion of total amount.
        </div>
        <div className="text-sm font-medium mt-4">Pay the rest 45 days before check-in</div>
        <div className="text-sm text-gray-800">
          Your original payment method will be charged on the second payment date.
        </div>
        <div className="text-sm font-medium mt-4">Payment is automatic</div>
        <div className="text-sm text-gray-800">
          There is no action needed on your part to make the second payment. We will charge your
          payment method automatically on the second payment date.
        </div>
      </div>
    </ResponsiveDialog>
  );
});

export default PaymentScheduleDialog;
