'use client';

import { memo, useState } from 'react';

import LoadingSpinner from '@/components/ui/loading-spinner';
import { Separator } from '@/components/ui/separator';
import { useRequestToBook } from '@/contexts/RequestToBookContext';
import { currencyFormatter } from '@/utils/common';

import classNames from 'classnames';
import { format } from 'date-fns';

import PaymentScheduleDialog from './PaymentScheduleDialog';

type Props = {
  isAuthenticated?: boolean;
};

const PaymentSchedule = memo(({ isAuthenticated }: Props) => {
  const [showPaymentSchedule, setShowPaymentSchedule] = useState<boolean>(false);
  const { bookingAvailabilityData, isFetchingBookingDetails } = useRequestToBook();

  return (
    <>
      <div
        className={classNames('w-full md:w-[500px]', {
          'md:border-solid md:border-grey-border rounded-[10px] mb-5 md:p-3': isAuthenticated,
        })}
      >
        <p className="leading-7 font-semibold m-0 text-sm">Payment Schedule</p>
        <Separator className="my-2 hidden md:block" />
        {!bookingAvailabilityData && isFetchingBookingDetails ? (
          <LoadingSpinner className="w-full my-4" />
        ) : (
          <>
            {isAuthenticated && (bookingAvailabilityData?.payment_schedule ?? []).length > 1 && (
              <p className="text-sm font-medium m-0">Pay part now, part later</p>
            )}
            {(bookingAvailabilityData?.payment_schedule ?? []).map((_ps, index) => (
              <div key={index} className="flex items-center justify-between">
                <p className="text-sm m-0">
                  {index === 0
                    ? `Due when booking is confirmed:`
                    : `Due on ${format(_ps[0], 'LLL d, yyyy')}:`}
                </p>
                <p className="text-sm m-0">{currencyFormatter.format(Number(_ps[1] ?? 0))}</p>
              </div>
            ))}
            {(bookingAvailabilityData?.payment_schedule ?? []).length > 1 && (
              <p
                className="underline cursor-pointer text-sm m-0"
                onClick={(e) => {
                  e.preventDefault();
                  setShowPaymentSchedule(true);
                }}
              >
                More info
              </p>
            )}
          </>
        )}
      </div>
      {showPaymentSchedule && (
        <PaymentScheduleDialog
          open={showPaymentSchedule}
          onToggle={() => setShowPaymentSchedule(false)}
        />
      )}
    </>
  );
});

export default PaymentSchedule;
