'use client';

import { ReactNode, useState } from 'react';

import { DateRange } from 'react-day-picker';
import { DayPicker } from 'react-day-picker';

import { Button, buttonVariants } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';

import { addDays } from 'date-fns';

type Props = {
  show: boolean;
  onToggle: (_o: boolean) => void;
  trigger: ReactNode;
};

const DatePicker = ({ show, onToggle, trigger }: Props) => {
  const [open, setOpen] = useState<boolean>(false);
  const [date, setDate] = useState<DateRange | undefined>({
    from: new Date(),
    to: addDays(new Date(), 20),
  });
  console.log('show', show);
  return (
    <Popover open={show} onOpenChange={onToggle}>
      <PopoverTrigger className="w-full p-0 border-none">{trigger}</PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="start">
        <DayPicker
          mode="range"
          selected={date}
          onSelect={setDate}
          numberOfMonths={2}
          classNames={{
            months: 'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
            day: cn(
              buttonVariants({ variant: 'default' }),
              'h-8 w-8 p-0 font-normal aria-selected:opacity-100',
            ),
          }}
        />
        <Button>Save</Button>
      </PopoverContent>
    </Popover>
  );
};

export default DatePicker;
