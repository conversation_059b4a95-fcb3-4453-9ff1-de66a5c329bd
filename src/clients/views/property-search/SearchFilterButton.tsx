'use client';

import { useCallback, useMemo, useState } from 'react';

import { PencilIcon } from '@heroicons/react/24/outline';

import { GuestsValues } from '@/clients/components/common/GuestSelector';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { parseDateString } from '@/utils/common';

import classNames from 'classnames';
import dynamic from 'next/dynamic';
import { useSearchParams } from 'next/navigation';
import { twMerge } from 'tailwind-merge';

const SearchFilterDialog = dynamic(() => import('./SearchFilterDialog'), {
  loading: () => (
    <div className="flex items-center justify-center absolute inset-0">
      <LoadingSpinner />
    </div>
  ),
});

type Props = {
  defaultAreas?: string[];
  bedrooms?: number;
  defaultRange?: [number, number];
  defaultAmenities?: string[];
  defaultGuestValues?: GuestsValues;
  filterCount: number;
  className?: string;
  hideLabel?: boolean;
};

const SearchFilterButton = ({
  defaultAreas,
  bedrooms,
  defaultRange,
  defaultAmenities,
  defaultGuestValues,
  filterCount,
  className = '',
  hideLabel,
}: Props) => {
  const searchParams = useSearchParams();
  const [show, setShow] = useState<boolean>(false);
  const defaultDateRange = useMemo(
    () =>
      searchParams?.get('date_min') && searchParams?.get('date_max')
        ? {
            to: parseDateString(searchParams?.get('date_max') ?? ''),
            from: parseDateString(searchParams?.get('date_min') ?? ''),
          }
        : undefined,
    [searchParams],
  );
  const onToggle = useCallback(() => {
    setShow(!show);
  }, [show]);

  return (
    <>
      <div
        className={classNames(
          twMerge(
            'text-sm text-primary-text flex items-center justify-between leading-[19px] cursor-pointer gap-x-1 max-w-[120px] lg:w-full ml-2 lg:ml-0',
            className,
          ),
        )}
        onClick={onToggle}
        onKeyDown={onToggle}
        role="button"
        tabIndex={0}
      >
        {!hideLabel && (
          <span className="inline-flex items-center">
            Filters {<span className="pl-1">({filterCount})</span>}
          </span>
        )}
        <PencilIcon
          className={classNames('w-4 h-4', {
            'ml-1 md:ml-0': !hideLabel,
          })}
        />
      </div>
      {show && (
        <SearchFilterDialog
          onToggle={onToggle}
          defaultAmenities={defaultAmenities}
          bedrooms={bedrooms}
          defaultAreas={defaultAreas}
          defaultRange={defaultRange}
          defaultDateRange={defaultDateRange}
          defaultGuestValues={defaultGuestValues}
        />
      )}
    </>
  );
};

export default SearchFilterButton;
