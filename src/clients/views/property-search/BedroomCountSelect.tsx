'use client';

import { memo, useCallback } from 'react';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Nullable } from '@/types/common';
import { aRandomUUID } from '@/utils/common';

export const BEDROOM_COUNT_OPTIONS = [
  { id: aRandomUUID(), text: '1 bedroom', value: '1' },
  { id: aRandomUUID(), text: '2 bedrooms', value: '2' },
  { id: aRandomUUID(), text: '3 bedrooms', value: '3' },
  { id: aRandomUUID(), text: '4 bedrooms', value: '4' },
  { id: aRandomUUID(), text: '5 bedrooms', value: '5' },
  { id: aRandomUUID(), text: '6 bedrooms', value: '6' },
  { id: aRandomUUID(), text: '7 bedrooms', value: '7' },
  { id: aRandomUUID(), text: '8+ bedrooms', value: '8' },
];

type Props = {
  bedCount: Nullable<number>;
  setBedCount: (_c: Nullable<number>) => void;
};

const BedroomCountSelect = ({ bedCount, setBedCount }: Props) => {
  const onSort = useCallback(
    (value: string) => {
      console.log('the value os', value);
      setBedCount(Number(value));
    },
    [setBedCount],
  );
  return (
    <Select value={bedCount?.toString()} onValueChange={onSort}>
      <SelectTrigger className="w-full border-none h-full shadow-none">
        <SelectValue placeholder="Bedrooms" />
      </SelectTrigger>
      <SelectContent>
        {BEDROOM_COUNT_OPTIONS.map((_bed, index) => (
          <SelectItem key={index} value={_bed.value}>
            {_bed.text}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default memo(BedroomCountSelect);
