'use client';

import { memo, useCallback } from 'react';

import { BarsArrowDownIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';

import classNames from 'classnames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

type Props = {
  sortQuery?: string;
  className?: string;
};

const PriceSort = ({ sortQuery, className = '' }: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const sort = searchParams?.get('price') ?? sortQuery ?? 'low';

  const onSort = useCallback(() => {
    const current = new URLSearchParams(searchParams?.toString());
    const newSort = sort === 'low' ? 'high' : 'low';
    current.set('price', newSort);

    router.push(`${pathname}?${current.toString()}`);
  }, [pathname, router, searchParams, sort]);

  return (
    <Button
      intent="ghost"
      className={classNames(
        'border border-solid !border-disabled rounded-sm h-10 flex items-center gap-x-2 text-sm px-4 font-normal',
        className,
      )}
      onClick={onSort}
    >
      <BarsArrowDownIcon
        className={classNames('w-4 h-4 transition-all', {
          'rotate-180': sort === 'low',
        })}
      />
      Price
    </Button>
  );
};

export default memo(PriceSort);
