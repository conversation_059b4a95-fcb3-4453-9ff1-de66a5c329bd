'use client';

import { memo, useCallback } from 'react';

import Checkbox from '@/components/ui/checkbox';

const AMENITIES = [
  { id: 'air_conditioning', text: 'Air conditioning', value: 'air_conditioning' },
  { id: 'pool', text: 'Pool', value: 'pool' },
  { id: 'pet_friendly', text: 'Pet Friendly', value: 'pet_friendly' },
  { id: 'water_front', text: 'Waterfront', value: 'water_front' },
  { id: 'water_view', text: 'Waterview', value: 'water_view' },
  { id: 'grill', text: 'Grill', value: 'grill' },
  { id: 'tennis', text: 'Tennis', value: 'tennis' },
  { id: 'king_bed', text: 'King bed', value: 'king_bed' },
];

type Props = {
  amenities: string[];
  setAmenities: (_a: string[]) => void;
};

const AmenitiesCheckboxes = ({ amenities, setAmenities }: Props) => {
  const onChange = useCallback(
    (_n: string) => {
      const hasId = amenities.includes(_n);
      setAmenities(hasId ? amenities.filter((_a) => _a !== _n) : [...amenities, _n]);
    },
    [amenities, setAmenities],
  );

  return (
    <div className="py-2">
      <p className="m-0 text-xl font-medium">Amenities</p>
      <div className="flex flex-wrap gap-x-3">
        {AMENITIES.map((_a, index) => (
          <div key={index} className="py-1 cursor-pointer flex items-center gap-x-1">
            <Checkbox
              id={_a.id}
              checked={amenities.includes(_a.value)}
              onChange={() => onChange(_a.value)}
            />
            <label htmlFor={_a.id} className="cursor-pointer text-sm text-metal-gray">
              {_a.text}
            </label>
          </div>
        ))}
      </div>
    </div>
  );
};

export default memo(AmenitiesCheckboxes);
