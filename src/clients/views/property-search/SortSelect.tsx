'use client';

import { memo, useState } from 'react';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { usePathname, useRouter, useSearchParams } from 'next/navigation';

type Props = {
  sortQuery?: string;
};

const SortSelect = ({ sortQuery }: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();
  const [sort, setSortValue] = useState<string>(sortQuery ?? '');

  const onSort = (value: string) => {
    const current = new URLSearchParams(Array.from(searchParams?.entries() ?? []));

    if (!value) {
      current.delete('price');
    } else {
      current.set('price', value);
    }

    const search = current.toString();
    const query = search ? `?${search}` : '';
    const route = `${pathname}${query}`;
    setSortValue(value);
    router.push(route);
  };
  return (
    <Select value={sort} onValueChange={onSort}>
      <SelectTrigger className="w-full border-none h-full shadow-none">
        <SelectValue placeholder="Sort by" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="low">Price: Low to High</SelectItem>
        <SelectItem value="high">Price: High to Low</SelectItem>
        <SelectItem value="high_rating">Rating</SelectItem>
      </SelectContent>
    </Select>
  );
};

export default memo(SortSelect);
