'use client';

import { useCallback, useState } from 'react';

import LoadingSpinner from '@/components/ui/loading-spinner';

import dynamic from 'next/dynamic';
import { twMerge } from 'tailwind-merge';

const GeneralInquiryFormDialog = dynamic(() => import('./GeneralInquiryDialog'), {
  ssr: false,
  loading: () => (
    <div className="fixed bg-black/40 inset-0 flex items-center justify-center text-white z-[999]">
      <LoadingSpinner className="w-10 h-10" />
    </div>
  ),
});

type Props = {
  className?: string;
};

const GeneralInquiryButton = ({ className }: Props) => {
  const [showForm, setShowForm] = useState<boolean>(false);
  const onToggle = useCallback(() => {
    setShowForm((_f) => !_f);
  }, []);

  return (
    <>
      <p
        className={twMerge('text-[#334155] underline text-sm m-0 cursor-pointer', className)}
        onClick={onToggle}
      >
        Need help finding the right place?
      </p>
      {showForm && <GeneralInquiryFormDialog onToggle={onToggle} />}
    </>
  );
};

export default GeneralInquiryButton;
