'use client';

import { memo, useCallback, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { PopoverContentProps } from '@radix-ui/react-popover';

import Checkbox from '@/components/ui/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

type Props = {
  areas?: string[];
  setAreas: (areas: string[]) => void;
  className?: string;
  popoverClassName?: string;
  popoverContentProps?: PopoverContentProps;
  placeholderText?: string;
};

export const NANTUCKET_NEIGHBORHOODS = [
  { id: '1', name: 'Brant Point' },
  { id: '2', name: '<PERSON><PERSON>' },
  { id: '3', name: '<PERSON>' },
  { id: '4', name: '<PERSON><PERSON>' },
  { id: '5', name: 'Edge of Town' },
  { id: '6', name: 'Fishers Landing' },
  { id: '7', name: 'Hummock Pond' },
  { id: '8', name: '<PERSON><PERSON><PERSON>' },
  { id: '9', name: 'Madequecham' },
  { id: '10', name: 'Miacomet' },
  { id: '11', name: 'Mid Island' },
  { id: '12', name: 'Monomoy' },
  { id: '13', name: 'Nashaquisset' },
  { id: '14', name: 'Naushop' },
  { id: '15', name: 'Pocomo' },
  { id: '16', name: 'Polpis' },
  { id: '17', name: 'Quaise' },
  { id: '18', name: 'Quidnet' },
  { id: '19', name: 'Sconset' },
  { id: '20', name: 'Shimmo' },
  { id: '26', name: 'South of Town' },
  { id: '21', name: 'Surfside' },
  { id: '22', name: 'Tom Nevers' },
  { id: '23', name: 'Town' },
  { id: '24', name: 'Wauwinet' },
];

const AreaFilter = ({
  areas = [],
  setAreas,
  className = '',
  popoverClassName = '',
  popoverContentProps,
  placeholderText,
}: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onClickedTrigger = useCallback((e: any) => {
    e.preventDefault();
  }, []);

  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  const onSelectAll = useCallback(() => {
    if (areas.length === NANTUCKET_NEIGHBORHOODS.length) {
      setAreas([]);
    } else {
      setAreas(NANTUCKET_NEIGHBORHOODS.map((_a) => _a.name));
    }
  }, [areas.length, setAreas]);

  const onChange = useCallback(
    (_n: string) => {
      const hasId = areas.includes(_n);
      setAreas(hasId ? areas.filter((_a) => _a !== _n) : [...areas, _n]);
    },
    [areas, setAreas],
  );

  return (
    <Popover modal open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild onClick={onClickedTrigger}>
        <div
          onClick={onClick}
          onKeyDown={onClick}
          role="button"
          tabIndex={0}
          className={cn(
            'flex items-center justify-between px-2 pt-1.5 pb-2.5 bg-white rounded cursor-pointer gap-x-2',
            className,
          )}
        >
          {placeholderText ?? 'Area'}
          <ChevronDownIcon className={`w-4 h-4 text-[#6D7381] ${open && 'rotate-180'}`} />
        </div>
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          'min-w-[380px] border border-solid border-english-manor border-opacity-40',
          popoverClassName,
        )}
        side="bottom"
        align="start"
        avoidCollisions={false}
        {...popoverContentProps}
      >
        <>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="check-all"
              onChange={onSelectAll}
              checked={areas.length === NANTUCKET_NEIGHBORHOODS.length}
            />
            <label htmlFor="check-all" className="cursor-pointer">
              Check All
            </label>
          </div>
          <Separator className="my-2" />
          <div className="h-[300px] overflow-y-auto grid grid-cols-2 gap-2">
            {NANTUCKET_NEIGHBORHOODS?.map((_n, index) => (
              <div className="py-1 cursor-pointer flex items-center gap-x-2" key={index}>
                <Checkbox
                  checked={areas.includes(_n.name)}
                  id={_n.id}
                  onChange={() => onChange(_n.name)}
                />
                <label htmlFor={_n.id} className="cursor-pointer">
                  {_n.name}
                </label>
              </div>
            ))}
          </div>
        </>
      </PopoverContent>
    </Popover>
  );
};

export default memo(AreaFilter);
