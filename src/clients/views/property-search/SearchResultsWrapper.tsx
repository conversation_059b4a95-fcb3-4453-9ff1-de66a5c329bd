'use client';

import { useCallback, useEffect } from 'react';

import { EnhancedEcomItem, NRProperties } from '@/types/properties';
import {
  formatSearchFiltersEcommerceData,
  formatSeoProperty,
  pushEcommerceDataLayer,
} from '@/utils/enhancedEcomAnanalytics';
import {
  formatKlaviyoSearchEvent,
  identifyUser,
  KlaviyoEvents,
  pushKlaviyoData,
} from '@/utils/klaviyoAnalytics';

type Props = {
  children: React.ReactNode;
  data: NRProperties[];
  params: { [key: string]: string };
};

const SearchResultsWrapper = ({ children, data, params }: Props) => {
  const pushEventsToSegment = useCallback(() => {
    if (data.length > 0) {
      const landingPageSeoData: EnhancedEcomItem[] = [];
      const filterSeo = formatSearchFiltersEcommerceData(params);
      console.debug(
        'will push to Klaviyo ===>',
        KlaviyoEvents.LISTING_SEARCHED,
        window.analytics,
        localStorage.getItem('ajs_user_id'),
      );
      pushKlaviyoData(KlaviyoEvents.LISTING_SEARCHED, formatKlaviyoSearchEvent(data, params));
      data.forEach((property: NRProperties, index) => {
        const item: EnhancedEcomItem = formatSeoProperty(
          property as any,
          index,
          'Search',
          filterSeo,
        );
        landingPageSeoData.push(item);
      });
      if (landingPageSeoData.length > 0) {
        pushEcommerceDataLayer('view_item_list', landingPageSeoData);
      }
    }
  }, [data, params]);

  useEffect(() => {
    const intervalId = setInterval(() => {
      if (window.analytics) {
        const ulgi = params?.ulgi;
        if (ulgi) {
          identifyUser(Buffer.from(ulgi as string, 'base64').toString('ascii'));
          console.debug('Identified ===>', Buffer.from(ulgi as string, 'base64').toString('ascii'));
          setTimeout(() => {
            pushEventsToSegment();
          }, 5000);
        } else {
          pushEventsToSegment();
        }
        clearInterval(intervalId);
      }
    }, 100);

    return () => clearInterval(intervalId);
  }, [params?.ulgi, pushEventsToSegment]);

  return <>{children}</>;
};

export default SearchResultsWrapper;
