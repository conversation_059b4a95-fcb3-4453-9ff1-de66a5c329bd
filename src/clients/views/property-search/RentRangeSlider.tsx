'use client';

import React, { memo } from 'react';

import { Slider } from '@/components/ui/slider';
import InputLabel from '@/ui/atoms/InputLabel';
import { currencyFormatterRound } from '@/utils/amenity';

const formatRentRangeText = (start: number, end: number) => {
  return `${currencyFormatterRound.format(start)} - ${currencyFormatterRound.format(end)}`;
};

type Props = {
  range?: [number, number];
  setRange: (_r: [number, number]) => void;
};

const RentRangeSlider = ({ range, setRange }: Props) => {
  const handleSliderChange = (newValue: [number, number]) => {
    setRange(newValue);
  };

  return (
    <div className="md:min-w-[350px]">
      <InputLabel className="font-medium text-default">Nightly Price</InputLabel>
      <div className="w-100 h-8 flex items-center mb-2">
        <InputLabel className="mb-0">$0</InputLabel>
        <Slider
          className="w-full h-10 mx-2"
          value={[range?.[0] ?? 0, range?.[1] ?? 5000]}
          max={5000}
          step={100}
          onValueChange={handleSliderChange}
        />
        <InputLabel className="mb-0">$5000+</InputLabel>
      </div>
      <InputLabel>
        Selected Range: {formatRentRangeText(range?.[0] ?? 0, range?.[1] ?? 5000)}
      </InputLabel>
    </div>
  );
};

export default memo(RentRangeSlider);
