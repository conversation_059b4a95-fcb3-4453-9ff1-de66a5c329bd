'use client';

import { memo, useCallback } from 'react';

import Button from '@/clients/ui/Button';

import classNames from 'classnames';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';

type Props = {
  sortQuery?: string;
  className?: string;
  isMapView?: boolean;
};

const MapviewSwitchButton = ({ className = '', isMapView }: Props) => {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const router = useRouter();

  const OnClick = useCallback(() => {
    const current = new URLSearchParams(searchParams?.toString());
    const newView = isMapView ? 'list' : 'map';
    current.set('view', newView);

    router.push(`${pathname}?${current.toString()}`);
  }, [isMapView, pathname, router, searchParams]);

  return (
    <Button
      intent="ghost"
      className={classNames(
        'border border-solid !border-disabled rounded-sm h-10 flex items-center gap-x-2 text-sm px-4 font-normal',
        className,
      )}
      onClick={OnClick}
    >
      {isMapView ? 'List View' : 'Map View'}
    </Button>
  );
};

export default memo(MapviewSwitchButton);
