'use client';

import { memo } from 'react';

import { Marker } from 'react-map-gl';

import { currencyFormatter } from '@/utils/common';

type Props = {
  selectedMarker?: any;
};

const SelectedMarker = memo(({ selectedMarker }: Props) => {
  if (!selectedMarker) return null;
  return (
    <Marker
      latitude={Number(selectedMarker.latitude)}
      longitude={Number(selectedMarker.longitude)}
      anchor="bottom"
    >
      <div
        id="MapIconSelected"
        className="p-[3.5px] z-[9] rounded-[5%] bg-[#15a5e5] border-[0.2px] border-[#e4e4e4] cursor-pointer font-semibold shadow-[ -2px_-1px_53px_-10px_rgba(0,0,0,0.75)]"
      >
        <p id="MapIconSelectedP" className="text-[0.7rem] text-white font-[Poppins] p-0 m-0">
          {currencyFormatter.format(selectedMarker?.averageNightlyRate)}
        </p>
      </div>
    </Marker>
  );
});

export default SelectedMarker;
