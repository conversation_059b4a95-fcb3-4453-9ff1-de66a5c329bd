'use client';

import { memo, useCallback } from 'react';

import { Marker } from 'react-map-gl';

import { NRProperties } from '@/types/properties';
import { currencyFormatterRound } from '@/utils/common';

type Props = {
  properties: NRProperties[];
  setPopupInfo: (val: any) => void;
  selectedMarker: null | any;
  setSelectedMarker: (val: any) => void;
  setActiveIndex: (val: null | number) => void;
};

const Markers = ({ properties, setPopupInfo, setSelectedMarker, setActiveIndex }: Props) => {
  const onClickMarker = useCallback(
    (e: any, property: NRProperties, index: number) => {
      e.originalEvent.stopPropagation();
      setPopupInfo(property);
      setSelectedMarker(property);
      setActiveIndex(index);
    },
    [setActiveIndex, setPopupInfo, setSelectedMarker],
  );

  return (
    <div>
      {properties.map((property, index) => (
        <Marker
          key={index}
          latitude={Number(property.latitude)}
          longitude={Number(property.longitude)}
          anchor="bottom"
          onClick={(e) => onClickMarker(e, property, index)}
        >
          <div
            id="MapIcon"
            className="p-1 rounded-[5%] bg-white border-solid border-[0.2px] border-[#e4e4e4] cursor-pointer font-medium transition-transform duration-200 ease-in-out shadow-[1px_6px_15px_-1px_rgba(0,0,0,0.285)] hover:scale-112 selected:bg-[#15a5e5]"
          >
            <p
              id="MapIconP"
              className="text-[0.7rem] text-[#15a5e5] font-[Poppins] p-0 m-0 selected:text-white"
            >
              {currencyFormatterRound.format(property?.averageNightlyRate ?? 0)}
            </p>
          </div>
        </Marker>
      ))}
    </div>
  );
};

export default memo(Markers);
