'use client';

import React, { useRef, useState, useCallback, memo } from 'react';

import { NavigationControl, Popup } from 'react-map-gl';

import multiLayerIcon from '@/common/assets/img/icons/layers-2x.png';
import satelliteBasemapIcon from '@/common/assets/img/icons/satellite.png';
import streetsBasemapIcon from '@/common/assets/img/icons/streets.png';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { getPropertyListingViewPortSearchParams } from '@/services/properties';
import { NRProperties } from '@/types/properties';

import 'mapbox-gl/dist/mapbox-gl.css';
import dynamic from 'next/dynamic';
import Image from 'next/image';

import MapViewPopupComponent from './MapViewPopupComponent';
import Markers from './Markers';
import SelectedMarker from './SelectedMarker';

// Dynamically import Map component for better performance
const Map = dynamic(() => import('react-map-gl'), { ssr: false });

const basemap = [
  'mapbox://styles/smaury/clbocbxbd002114p9p2qerfzg',
  'mapbox://styles/smaury/cldnqxkla000t01p5ilnj79ge',
];

const MAPBOX_TOKEN = process.env.NEXT_PUBLIC_REACT_APP_MAPBOX_TOKEN;

export type ViewPort = {
  latitude: number;
  longitude: number;
  zoom: number;
};

type Props = {
  properties?: NRProperties[];
  setProperties: (data: NRProperties[]) => void;
  viewport: ViewPort;
  activeIndex: null | number;
  setViewport: (_vp: any) => void;
  popupInfo: null | any;
  setPopupInfo: (val: any) => void;
  selectedMarker: null | any;
  setSelectedMarker: (val: any) => void;
  setActiveIndex: (val: null | number) => void;
  onResetZoom: () => void;
  params: { [key: string]: string };
};

const MapViewComponent = ({
  properties = [],
  viewport,
  setProperties,
  setViewport,
  popupInfo,
  setPopupInfo,
  selectedMarker,
  setSelectedMarker,
  activeIndex,
  setActiveIndex,
  onResetZoom,
  params,
}: Props) => {
  const [fetching, setFetching] = useState(false);

  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<any>(null);

  const onClosePopup = useCallback(() => {
    setPopupInfo(null);
    setSelectedMarker(null);
  }, [setPopupInfo, setSelectedMarker]);

  const getData = useCallback(async () => {
    try {
      if (!map.current) return;
      const zoom = Number(await map.current.getZoom());

      if (zoom > 11.5 || viewport.zoom > 11.5) {
        setFetching(true);
        setActiveIndex(null);
        const { lng, lat } = await map.current.getCenter();
        const { _sw, _ne } = await map.current.getBounds();
        const response = await getPropertyListingViewPortSearchParams(
          _sw.lng,
          _sw.lat,
          _ne.lng,
          _ne.lat,
          lng,
          lat,
          params,
        );
        setProperties(response?.results ?? []);
        setPopupInfo(null);
        setSelectedMarker(null);
        setTimeout(() => {
          setFetching(false);
        }, 300);
      } else {
        onResetZoom();
      }
    } catch (err) {
      console.error('Error fetching data from API:', err);
    }
  }, [
    viewport.zoom,
    setActiveIndex,
    params,
    setProperties,
    setPopupInfo,
    setSelectedMarker,
    onResetZoom,
  ]);

  return (
    <div
      id="MapContainer"
      ref={mapContainer}
      className="flex w-screen h-screen fixed justify-center overflow-hidden mapview-safari-mobile-bottom [&_.mapboxgl-ctrl-bottom-left]:bottom-10"
    >
      <Map
        ref={map}
        {...viewport}
        style={{ position: 'relative', width: '100%', height: '100%' }}
        mapStyle={basemap[0]}
        minZoom={10}
        maxZoom={18}
        maxBounds={[-70.48, 41.1, -69.62, 41.53]}
        onMove={(e) => setViewport(e.viewState)}
        onMoveEnd={getData}
        mapboxAccessToken={MAPBOX_TOKEN}
        attributionControl={false}
        doubleClickZoom={false}
        dragRotate={false}
      >
        <Markers
          properties={properties}
          setPopupInfo={setPopupInfo}
          setSelectedMarker={setSelectedMarker}
          setActiveIndex={setActiveIndex}
          selectedMarker={selectedMarker}
        />
        <SelectedMarker selectedMarker={selectedMarker} />
        <NavigationControl position="bottom-left" />
        {popupInfo && (
          <Popup
            longitude={Number(popupInfo.longitude)}
            latitude={Number(popupInfo.latitude)}
            onClose={onClosePopup}
            closeButton={false}
            className="z-[99] bottom-[15vh] !left-[10px] !right-[10px] !top-auto !transform-none overflow-hidden rounded-xl !w-[calc(100vw-20px)] !max-w-[calc(100vw-20px)] h-auto [&_.mapboxgl-popup-content]:w-full [&_.mapboxgl-popup-content]:rounded-xl [&_.mapboxgl-popup-tip]:hidden"
          >
            <MapViewPopupComponent popupInfo={popupInfo} setPopupInfo={setPopupInfo} />
          </Popup>
        )}
      </Map>
      {fetching && (
        <div className="flex items-center justify-center absolute inset-0">
          <LoadingSpinner />
        </div>
      )}
    </div>
  );
};

export default memo(MapViewComponent);
