'use client';

import React, { useRef, useState, useCallback } from 'react';

import useHasMounted from '@/hooks/useHasMounted';
import { NRProperties } from '@/types/properties';

import { useRouter } from 'next/navigation';

import MapViewComponent from './MapViewComponent';

export type ViewPort = {
  latitude: number;
  longitude: number;
  zoom: number;
};

type Props = {
  data?: NRProperties[];
  params: { [key: string]: string };
};

const MapViewWrapper = ({ data = [], params }: Props) => {
  const [properties, setProperties] = useState<null | any>(data.slice(0, 15));
  const [activeIndex, setActiveIndex] = useState<null | number>(null);
  const [popupInfo, setPopupInfo] = useState<null | any>(null);
  const [selectedMarker, setSelectedMarker] = useState<null | any>(null);
  const [viewport, setViewport] = useState<ViewPort>({
    latitude: 41.282318660486794,
    longitude: -70.10102017636753,
    zoom: 11.5,
  });

  const onResetZoom = useCallback(() => {
    setProperties(data.slice(0, 15));
    setPopupInfo(null);
    setSelectedMarker(null);
  }, [data]);

  return (
    <MapViewComponent
      properties={properties}
      setProperties={setProperties}
      viewport={viewport}
      setViewport={setViewport}
      popupInfo={popupInfo}
      setPopupInfo={setPopupInfo}
      selectedMarker={selectedMarker}
      setSelectedMarker={setSelectedMarker}
      activeIndex={activeIndex}
      setActiveIndex={setActiveIndex}
      onResetZoom={onResetZoom}
      params={params}
    />
  );
};

export default MapViewWrapper;
