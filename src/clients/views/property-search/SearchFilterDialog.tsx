'use client';

import { useCallback, useMemo, useState } from 'react';

import { Cross2Icon } from '@radix-ui/react-icons';
import { DateRange } from 'react-day-picker';

import DateRangePicker from '@/clients/components/common/DateRangePicker';
import DateRangePickerMobile from '@/clients/components/common/DateRangePicker/DateRangePickerMobile';
import GuestsSelector, { GuestsValues } from '@/clients/components/common/GuestSelector';
import Button from '@/clients/ui/Button';
import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Nullable } from '@/types/common';
import { getStringSingularPlural } from '@/utils/common';
import { getSearchQueryParamsFromFilters } from '@/utils/filters';

import classNames from 'classnames';
import { useRouter, useSearchParams } from 'next/navigation';

import AmenitiesCheckboxes from './AmenitiesCheckboxes';
import AreaFilter from './AreaFilter';
import BedroomCountSelect from './BedroomCountSelect';
import RentRangeSlider from './RentRangeSlider';

type Props = {
  onToggle: () => void;
  defaultAreas?: string[];
  bedrooms?: number;
  defaultRange?: [number, number];
  defaultAmenities?: string[];
  defaultDateRange?: DateRange;
  defaultGuestValues?: GuestsValues;
};

export type FilterFormValues = {
  date?: DateRange;
  guests: GuestsValues;
  areas: string[];
  bedrooms: Nullable<number>;
  range?: [number, number];
  amenities: string[];
};

const SearchFilterDialog = ({
  onToggle,
  defaultAreas = [],
  bedrooms,
  defaultRange,
  defaultAmenities = [],
  defaultDateRange,
  defaultGuestValues,
}: Props) => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formState, setFormState] = useState<FilterFormValues>({
    guests: {
      adults: defaultGuestValues?.adults ?? 1,
      children: defaultGuestValues?.children ?? 0,
    },
    areas: defaultAreas,
    bedrooms: bedrooms ?? null,
    range: defaultRange,
    amenities: defaultAmenities,
    date: defaultDateRange,
  });

  const dateRangePickerText = useMemo(
    () =>
      formState?.date?.from && formState?.date?.to
        ? 'Check in - Check out (1)'
        : 'Check in - Check out',
    [formState?.date?.from, formState?.date?.to],
  );

  const guestSelectorText = useMemo(
    () =>
      getStringSingularPlural(
        'Guest',
        'Guests',
        formState.guests.adults + formState.guests.children,
      ),
    [formState.guests.adults, formState.guests.children],
  );

  const onChangeDateRangePicker = useCallback(
    (value?: DateRange) => {
      setFormState({
        ...formState,
        date: value,
      });
    },
    [formState],
  );

  const onChangeGuestValues = useCallback(
    (value: GuestsValues) => {
      setFormState({
        ...formState,
        guests: value,
      });
    },
    [formState],
  );

  const onChangeNeighborhood = useCallback(
    (value: string[]) => {
      setFormState({
        ...formState,
        areas: value,
      });
    },
    [formState],
  );

  const onChangeBedroomCount = useCallback(
    (value: Nullable<number>) => {
      setFormState({
        ...formState,
        bedrooms: value,
      });
    },
    [formState],
  );

  const onChangeRentRange = useCallback(
    (value: [number, number]) => {
      setFormState({
        ...formState,
        range: value,
      });
    },
    [formState],
  );

  const onChangeAmenities = useCallback(
    (value: string[]) => {
      setFormState({
        ...formState,
        amenities: value,
      });
    },
    [formState],
  );

  const onApply = useCallback(() => {
    const queryParams = getSearchQueryParamsFromFilters(formState);
    router.replace(
      `/property-search${queryParams.length > 0 ? '?' : ''}${queryParams.join('&')}${
        searchParams?.get('view') ? `&view=${searchParams?.get('view')}` : ''
      }`,
    );
    onToggle();
  }, [formState, onToggle, router, searchParams]);

  const onClearFilter = useCallback(() => {
    setFormState({
      guests: { adults: 1, children: 0 },
      areas: [],
      bedrooms: null,
      amenities: [],
    });
  }, []);

  return (
    <Dialog open onOpenChange={onToggle}>
      <DialogContent
        className="p-0 w-screen h-screen max-w-[100dvw] md:h-auto md:min-w-[220px] md:max-w-[600px] py-10 md:py-0"
        hideCloseButton
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <div className="w-full">
          <div className="px-[30px] py-4 relative font-medium text-xl leading-[140%] tracking-[0.5px]">
            Search filters
            <Button className="absolute right-[30px] !p-0" intent="ghost" onClick={onToggle}>
              <Cross2Icon className="h-5 w-5" />
            </Button>
          </div>
          <Separator />
          <div className="px-4 md:px-[30px] py-[30px] h-[72vh] md:h-auto overflow-y-scroll">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-2 gap-y-4">
              <div className="relative border border-solid border-disabled rounded-sm">
                <DateRangePicker
                  date={formState?.date}
                  setDate={onChangeDateRangePicker}
                  className="w-full text-sm hidden md:flex"
                  title={dateRangePickerText}
                />
                <DateRangePickerMobile
                  date={formState?.date}
                  setDate={onChangeDateRangePicker}
                  className="w-full md:hidden"
                  contentClassName="mt-0 top-0 bottom-0 py-10 h-screen [&_*_.calendar-wrapper]:h-[60vh]"
                  title={dateRangePickerText}
                />
              </div>
              <div className="relative border border-solid border-disabled rounded-sm">
                <GuestsSelector
                  guestsValues={formState.guests}
                  setGuestsValues={onChangeGuestValues}
                  className="w-full text-sm"
                  title={
                    <span
                      className={classNames({
                        'text-metal-gray':
                          formState.guests.adults + formState.guests.children === 1,
                      })}
                    >
                      {guestSelectorText}
                    </span>
                  }
                />
              </div>
              <div className="relative border border-solid border-disabled rounded-sm">
                <AreaFilter areas={formState.areas} setAreas={onChangeNeighborhood} />
              </div>
              <div className="relative border border-solid border-disabled rounded-sm">
                <BedroomCountSelect
                  bedCount={formState.bedrooms}
                  setBedCount={onChangeBedroomCount}
                />
              </div>
            </div>
            <div className="py-2 my-2">
              <RentRangeSlider range={formState?.range} setRange={onChangeRentRange} />
            </div>
            <AmenitiesCheckboxes amenities={formState.amenities} setAmenities={onChangeAmenities} />
          </div>
          <div className="fixed md:relative bottom-0 md:bottom-auto left-0 md:left-auto right-0 md:right-auto flex flex-col-reverse md:flex-row items-center justify-between gap-2 p-[30px] md:pt-0">
            <Button
              intent="outline"
              className="font-medium w-full md:w-6/12 text-carolina-blue hover:text-carolina-blue border-carolina-blue"
              onClick={onClearFilter}
            >
              Clear Filters
            </Button>
            <Button
              className="font-medium w-full md:w-6/12"
              onClick={onApply}
              // disabled={submitting}
              // isLoading={submitting}
            >
              Apply
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SearchFilterDialog;
