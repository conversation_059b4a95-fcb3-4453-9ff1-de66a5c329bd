'use client';

import uniqBy from 'lodash/uniqBy';
import { useMemo } from 'react';

import { Dialog, DialogContent, DialogDescription, DialogTitle } from '@/components/ui/dialog';
import useListingPhotos from '@/hooks/useListingPhotos';
import { PropertyPic } from '@/types/properties';

import Image from 'next/image';

type Props = {
  open: boolean;
  onToggle: () => void;
  nrPropertyId: number;
  defaultData: PropertyPic[];
};

const ViewAllPhotosDialog = ({ open, onToggle, nrPropertyId, defaultData }: Props) => {
  const { data } = useListingPhotos(nrPropertyId, defaultData);
  const images = useMemo(
    () => uniqBy([...(defaultData ?? []), ...(data ?? [])], 'nrPropertyPicId'),
    [data, defaultData],
  );

  const firstColumn = useMemo(() => images.filter((_, index) => index % 3 === 0), [images]);
  const secondColumn = useMemo(() => images.filter((_, index) => index % 3 === 1), [images]);
  const thirdColumn = useMemo(() => images.filter((_, index) => index % 3 === 2), [images]);

  return (
    <Dialog open={open} onOpenChange={onToggle}>
      <DialogContent
        className="p-0 w-screen min-h-screen max-w-screen bg-[#074059]/70 backdrop-blur-[2px]"
        closeButtonClassName="bg-black text-white w-10 h-10 top-6 right-3.5"
      >
        <DialogTitle className="hidden" />
        <DialogDescription className="hidden" />
        <div className="p-4 pt-[80px] md:p-10 h-screen bg-white overflow-y-scroll">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="grid gap-4">
              {firstColumn.map((_image, index) => (
                <Image
                  key={index}
                  src={_image.nrPropertyPicPath}
                  alt="Listing image"
                  loading="lazy"
                  quality={85}
                  sizes="100vw, 33vw"
                  className="h-auto w-full rounded-lg object-cover"
                  width={0}
                  height={0}
                />
              ))}
            </div>
            <div className="grid gap-4">
              {secondColumn.map((_image, index) => (
                <Image
                  key={index}
                  src={_image.nrPropertyPicPath}
                  alt="Listing image"
                  loading="lazy"
                  quality={85}
                  sizes="100vw, 33vw"
                  className="h-auto w-full rounded-lg object-cover"
                  width={0}
                  height={0}
                />
              ))}
            </div>
            <div className="grid gap-4">
              {thirdColumn.map((_image, index) => (
                <Image
                  key={index}
                  src={_image.nrPropertyPicPath}
                  alt="Listing image"
                  loading="lazy"
                  quality={85}
                  sizes="100vw, 33vw"
                  className="h-auto w-full rounded-lg object-cover"
                  width={0}
                  height={0}
                />
              ))}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ViewAllPhotosDialog;
