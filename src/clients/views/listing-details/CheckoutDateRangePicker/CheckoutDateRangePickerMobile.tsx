'use client';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';

import Button from '@/clients/ui/Button';
import { Skeleton } from '@/components/ui/skeleton';
import useHasMounted from '@/hooks/useHasMounted';
import usePropertyRentalRates from '@/hooks/usePropertyRentalRates';
import { BookingAvailabilityData } from '@/types/booking';
import { DatePickerInfoPopUp } from '@/types/calendar';
import { AvailabilityCalendarData, RentalRatesCalendarData } from '@/types/properties';
import { formatBookingFeesData } from '@/utils/booking';
import { getDateRangePickerMonths, isDateRangeSelected } from '@/utils/calendar';
import { currencyFormatterRound, Nullable, parseDateString } from '@/utils/common';
import {
  formatDateRangePickerRentalRates,
  getBlockedStartAndEndDates,
} from '@/utils/date-range-picker';

import classNames from 'classnames';
import {
  addYears,
  differenceInCalendarDays,
  endOfMonth,
  format,
  getMonth,
  isBefore,
  isValid,
  startOfDay,
  startOfMonth,
  subMonths,
} from 'date-fns';
import { twMerge } from 'tailwind-merge';

import CheckoutMobileCalendar from './CheckoutMobileCalendar';
import DateInfoPopup from './DateInfoPopup';

type Props = {
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  bookingAvailabilityData?: Nullable<BookingAvailabilityData>;
  onClose: () => void;
  rentalRates?: RentalRatesCalendarData[];
  availableCalendar?: AvailabilityCalendarData[];
  isFetchingBookingDetails?: boolean;
  propertyId: number;
  hideClose?: boolean;
  className?: string;
  calendarWrapperClassName?: string;
  showSave?: boolean;
  onSave?: () => void;
};

const CheckoutDateRangePickerMobile = ({
  date,
  setDate,
  onClose,
  availableCalendar = [],
  rentalRates: rentalRatesDefault = [],
  isFetchingBookingDetails,
  propertyId,
  hideClose,
  className = '',
  calendarWrapperClassName = '',
  showSave = false,
  onSave,
  bookingAvailabilityData,
}: Props) => {
  const [popUpInfo, setPopUpInfo] = useState<DatePickerInfoPopUp>({});
  const hasMounted = useHasMounted();
  const [startIndex, setStartIndex] = useState<number>(0);
  const [endIndex, setEndIndex] = useState<number>(4);
  const months = useMemo(() => getDateRangePickerMonths(), []);
  const { data, isLoading } = usePropertyRentalRates(
    propertyId,
    format(startOfMonth(new Date()), 'yyyy-MM-dd'),
    format(endOfMonth(subMonths(addYears(new Date(), 2), 1)), 'yyyy-MM-dd'),
  );

  const rentalRatesMap = useMemo(
    () =>
      isLoading && data.length === 0
        ? formatDateRangePickerRentalRates(rentalRatesDefault)
        : formatDateRangePickerRentalRates(data),
    [data, isLoading, rentalRatesDefault],
  );

  const { start: blockedStartDates, end: blockedEndDates } = useMemo(
    () =>
      getBlockedStartAndEndDates(
        availableCalendar.filter(
          (_a) =>
            !isBefore(
              startOfDay(parseDateString(_a.blockedFrom)),
              startOfDay(subMonths(new Date(), 1)),
            ) &&
            !isBefore(
              startOfDay(parseDateString(_a.blockedTo)),
              startOfDay(subMonths(new Date(), 1)),
            ),
        ),
      ),
    [availableCalendar],
  );

  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );

  const bookingData = useMemo(
    () =>
      bookingAvailabilityData && formatBookingFeesData(bookingAvailabilityData, '', numberOfNights),
    [bookingAvailabilityData, numberOfNights],
  );

  const onClearDates = useCallback(() => {
    setDate(undefined);
  }, [setDate]);

  const onConfirm = useCallback(() => {
    if (showSave && onSave) {
      onSave();
    } else {
      onClose();
    }
  }, [onClose, onSave, showSave]);

  const isCheckinSelected = useMemo(() => date?.from && isValid(date.from), [date?.from]);

  const isSelectedValid = useMemo(
    () => !!(date?.from && date?.to && isValid(date.from) && isValid(date.to)),
    [date?.from, date?.to],
  );

  const onClickLoadPrev = useCallback(() => {
    setStartIndex(startIndex - 4 < 0 ? 0 : startIndex - 4);
  }, [startIndex]);

  const onClickLoadMore = useCallback(() => {
    setEndIndex(endIndex + 4 > months.length ? months.length : endIndex + 4);
  }, [endIndex, months.length]);

  const headerText = useMemo(() => {
    if (isCheckinSelected) {
      if (isSelectedValid && date?.from && date?.to) {
        return `${differenceInCalendarDays(date?.to, date?.from)} nights`;
      }
      return 'Select check-out date';
    } else {
      return 'Select check-in date';
    }
  }, [isCheckinSelected, isSelectedValid, date?.from, date?.to]);

  useEffect(() => {
    const startMonth = date?.from && date?.to && getMonth(date.from);
    const startIndex = months.findIndex((_m) => _m.month === startMonth);
    if (!hasMounted) {
      setStartIndex(startIndex > 0 ? startIndex : 0);
      setEndIndex((startIndex > 0 ? startIndex : 0) + 4);
    }
  }, [date?.from, date?.to, hasMounted, months]);

  return (
    <>
      <div className={twMerge('p-4 bg-white rounded-t-[10px]', className)}>
        {!hideClose && (
          <Button onClick={onClose} intent="ghost" className="!p-0">
            <XCircleIcon className="w-6 h-6" />
          </Button>
        )}
        <div className="my-4 flex items-start justify-between">
          <div>
            <p className="m-0 text-xl font-medium">{headerText}</p>
            <p
              className={classNames('text-xs text-[#6D7380] m-0 mt-1 invisible', {
                '!visible': isSelectedValid && date?.from && date?.to,
              })}
            >
              {isSelectedValid && date?.from && date?.to
                ? `${format(date?.from, 'LLL d, yyyy')} - ${format(date?.to, 'LLL d, yyyy')}`
                : ''}
            </p>
          </div>
          <Button
            intent="ghost"
            className="px-0 text-carolina-blue text-xs py-1"
            onClick={onClearDates}
          >
            Clear date
          </Button>
        </div>
        <div className="flex items-center gap-x-1 pb-2 border border-t-0 border-l-0 border-r-0 border-b border-solid border-[#6D7380] text-[#6D7380] mb-4">
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            SUN
          </span>
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            MON{' '}
          </span>
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            TUE
          </span>
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            WED
          </span>
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            THU
          </span>
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            FRI
          </span>
          <span className="flex-1 text-center text-[10px] leading-[120%] tracking-[1.5px] py-1">
            SAT
          </span>
        </div>

        <div className={twMerge('overflow-y-auto h-[55dvh]', calendarWrapperClassName)}>
          {startIndex > 0 && (
            <Button
              intent="outline"
              className="w-full border-solid border-carolina-blue font-normal rounded-[32px] mb-6"
              onClick={onClickLoadPrev}
            >
              Load more dates
            </Button>
          )}
          {months.slice(startIndex, endIndex).map((_m, index) => (
            <CheckoutMobileCalendar
              key={index}
              month={_m.month}
              year={_m.year}
              selected={date}
              onSelectDate={setDate}
              availabilityData={availableCalendar}
              rentalRates={
                rentalRatesMap[
                  format(parseDateString(`${_m.year}-${_m.month + 1}-1`, 'yyyy-M-d'), 'MMM-yyyy')
                ]
              }
              blockedStartDates={blockedStartDates}
              blockedEndDates={blockedEndDates}
              setPopUpInfo={setPopUpInfo}
            />
          ))}
          {months.length > endIndex && (
            <Button
              intent="outline"
              className="w-full border-solid border-carolina-blue font-normal rounded-[32px]"
              onClick={onClickLoadMore}
            >
              Load more dates
            </Button>
          )}
        </div>
        <div className="mt-4 flex items-center justify-between">
          <div>
            {bookingData && isDateRangeSelected(date) ? (
              <>
                <span className="font-medium text-[24px]">
                  {currencyFormatterRound.format(bookingData?.averageNightlyRate ?? 0)}
                </span>{' '}
                <span className="text-xs">Per Night</span>
              </>
            ) : isFetchingBookingDetails ? (
              <>
                <Skeleton className="w-[120px] h-6 mb-2.5" />
              </>
            ) : (
              <p className="m-0">Add date for price</p>
            )}
          </div>
          <Button
            className="font-medium rounded-[32px] py-3"
            disabled={!isSelectedValid}
            onClick={onConfirm}
          >
            {showSave ? 'Save' : 'Confirm'}
          </Button>
        </div>
      </div>
      {(popUpInfo?.left || popUpInfo?.right) && popUpInfo?.bottom && (
        <DateInfoPopup popUpInfo={popUpInfo} setPopUpInfo={setPopUpInfo} />
      )}
    </>
  );
};

export default CheckoutDateRangePickerMobile;
