'use client';

import { memo, useMemo } from 'react';

import { DateRange } from 'react-day-picker';

import { DatePickerInfoPopUp } from '@/types/calendar';
import { AvailabilityCalendarData, RentalRatesCalendarData } from '@/types/properties';
import { getFirstDayOfMonth, getNumberOfDaysInMonth } from '@/utils/calendar';
import { parseDateString } from '@/utils/common';
import { getAvailabilityDataForCurrentMonth } from '@/utils/date-range-picker';

import classNames from 'classnames';
import { isSameDay } from 'date-fns';

import CheckoutDateItem from './CheckoutDateItem';

const MONTHS = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

type Props = {
  month: number;
  year: number;
  selected?: DateRange;
  onSelectDate: (_d?: DateRange) => void;
  availabilityData?: AvailabilityCalendarData[];
  rentalRates?: RentalRatesCalendarData[];
  setPopUpInfo?: (_i: DatePickerInfoPopUp) => void;
  blockedStartDates: Date[];
  blockedEndDates: Date[];
};

const CheckoutMobileCalendar = ({
  month,
  year,
  availabilityData = [],
  rentalRates = [],
  selected,
  onSelectDate,
  setPopUpInfo,
  blockedStartDates,
  blockedEndDates,
}: Props) => {
  const availabilityDataForCurrentMonth = useMemo(
    () => getAvailabilityDataForCurrentMonth(availabilityData, month + 1, year),
    [availabilityData, month, year],
  );

  const rentalDataForStartDate = useMemo(
    () =>
      rentalRates?.find(
        (_r) => selected?.from && isSameDay(parseDateString(_r.rateStartFrom), selected?.from),
      ),
    [rentalRates, selected?.from],
  );

  const numberOfDays = useMemo(() => getNumberOfDaysInMonth(year, month), [month, year]);
  const firstDayOfMonth = useMemo(() => getFirstDayOfMonth(year, month), [year, month]);
  const numberOfDaysLastMonth = useMemo(
    () =>
      month > 0 ? getNumberOfDaysInMonth(year, month - 1) : getNumberOfDaysInMonth(year - 1, 11),
    [year, month],
  );

  const totalSlots: JSX.Element[] = [];

  for (let i = numberOfDaysLastMonth - firstDayOfMonth; i < numberOfDaysLastMonth; i++) {
    totalSlots.push(<td key={`prev-${i}`} />);
  }

  for (let i = 0; i < numberOfDays; i++) {
    totalSlots.push(
      <CheckoutDateItem
        key={`day${i}`}
        dateObject={parseDateString(`${year}/${month + 1}/${i + 1}`, `yyyy/M/d`)}
        date={i + 1}
        active
        insideCurrentMonth={true}
        selected={selected}
        onSelectDate={onSelectDate}
        setPopUpInfo={setPopUpInfo}
        availabilityDataForCurrentMonth={availabilityDataForCurrentMonth}
        availabilityData={availabilityData}
        rentalRates={rentalRates}
        rentalDataForStartDate={rentalDataForStartDate}
        rentalRatesForDate={rentalRates.find((_r) =>
          isSameDay(
            parseDateString(_r.rateStartFrom),
            parseDateString(`${year}/${month + 1}/${i + 1}`, 'yyyy/M/d'),
          ),
        )}
        blockedStartDates={blockedStartDates}
        blockedEndDates={blockedEndDates}
      />,
    );
  }

  for (let i = 0; i < (42 - firstDayOfMonth - numberOfDays) % 7; i++) {
    totalSlots.push(<td key={`next-${i}`} />);
  }

  const rows: JSX.Element[][] = [];

  const weeks = Math.ceil(totalSlots.length / 7);
  for (let i = 0; i < weeks; i++) {
    const cells: JSX.Element[] = [];
    for (let j = 0; j < 7; j++) {
      const day = i * 7 + j;
      const row = totalSlots[day];
      cells.push(row || <td key={`blank${day}`}>{''}</td>);
    }
    let day = i * 7 - firstDayOfMonth;
    if (day < 0) {
      day = 0;
    }
    rows.push(cells);
  }

  return (
    <div className="Calendar mb-4">
      <div className="flex-center-between">
        <p className={classNames(`font-medium text-sm m-0`, {})}>
          {MONTHS[month]} {year}
        </p>
      </div>
      <table className="w-full">
        <tbody>
          {rows.map((d, i) => (
            <tr key={i} className="text-center">
              {d}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default memo(CheckoutMobileCalendar);
