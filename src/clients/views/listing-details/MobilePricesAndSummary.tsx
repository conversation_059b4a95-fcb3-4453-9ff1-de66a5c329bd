'use client';

import { useCallback, useState } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';
import { useBooking } from '@/contexts/BookingContext';
import { FormattedBookingData } from '@/types/booking';
import { Nullable } from '@/types/common';
import { PropertyDetails } from '@/types/properties';
import { getStringSingularPlural } from '@/utils/common';

import { format } from 'date-fns';

import MobileBottomDrawer from './MobileBottomDrawer';
import MobileSelectGuest from './MobileSelectGuest';
import PricesAndSummary from './PricesAndSummary';

type Props = {
  onToggle: () => void;
  onToggleDatePicker: () => void;
  bookingData: Nullable<FormattedBookingData>;
  petsAllowed?: boolean;
  property: PropertyDetails;
  capacity?: number;
  numberOfNights: number;
};

const MobilePricesAndSummary = ({
  onToggle,
  onToggleDatePicker,
  bookingData,
  property,
  petsAllowed,
  capacity,
  numberOfNights,
}: Props) => {
  const { guests, date, petCount, isPetSelected } = useBooking();
  const [openPets, setOpenPets] = useState<boolean>(false);

  const onTogglePetsDialog = useCallback(() => {
    setOpenPets(!openPets);
  }, [openPets]);
  return (
    <>
      <div className="p-4 bg-white rounded-t-[10px] border border-solid border-[#E5E5E5]">
        <div className="flex items-start justify-between">
          <p className="m-0 text-xl font-medium">Price Details</p>
          <Button onClick={onToggle} intent="ghost" className="!p-0">
            <XCircleIcon className="w-6 h-6" />
          </Button>
        </div>
        <PricesAndSummary bookingData={bookingData} numberOfNights={numberOfNights} />
        <p className="m-0 mb-1 pl-3.5 uppercase text-xs text-metal-gray">Date</p>
        <div className="border border-solid border-platinium rounded-[40px] px-3.5 py-2.5 flex items-center justify-between">
          <span>
            {date?.from && format(date.from, 'MMM d')}-{date?.to && format(date.to, 'd')}
          </span>
          <Button
            onClick={onToggleDatePicker}
            intent="ghost"
            className="font-normal text-sm text-foundation-blue !p-0"
          >
            Change
          </Button>
        </div>
        <p className="m-0 my-1 pl-3.5 uppercase text-xs text-metal-gray">Guests</p>
        <div className="border border-solid border-platinium rounded-[40px] px-3.5 py-2.5 flex items-center justify-between">
          <span>
            {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
            {getStringSingularPlural('Child', 'Children', guests.children)}
            {isPetSelected &&
              petCount > 0 &&
              `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
          </span>
          <Button
            onClick={onTogglePetsDialog}
            intent="ghost"
            className="font-normal text-sm text-foundation-blue !p-0"
          >
            Change
          </Button>
        </div>
        <p className="m-0 my-2 uppercase text-xs text-metal-gray">
          This property has a maximum of {capacity} guests.{' '}
          {!petsAllowed
            ? `Pets are not allowed.`
            : `Pets allowed with prior permission, fees may apply.`}
        </p>
        <Button className="w-full rounded-[32px]" onClick={onToggle}>
          Continue
        </Button>
      </div>
      <MobileBottomDrawer
        open={openPets}
        onToggle={onTogglePetsDialog}
        overlayClassName="z-[999]"
        contentClassName="z-[9999]"
      >
        {openPets && (
          <MobileSelectGuest
            property={property}
            onToggle={onTogglePetsDialog}
            petsAllowed={petsAllowed}
            capacity={capacity}
          />
        )}
      </MobileBottomDrawer>
    </>
  );
};

export default MobilePricesAndSummary;
