'use client';

import { ReactNode, useCallback, useEffect } from 'react';

import ProgressDialog from '@/clients/components/common/ProgressDialog';
import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import BackdropLoader from '@/components/common/Loader/BackdropLoader';
import { useBooking } from '@/contexts/BookingContext';
import { useProfile } from '@/contexts/selectors/app-context-selectors';
import useHasMounted from '@/hooks/useHasMounted';
import { ProgressStatus } from '@/types/common';
import { PropertyDetails } from '@/types/properties';
import { formatSeoProperty, pushEcommerceDataLayer } from '@/utils/enhancedEcomAnanalytics';
import { formatKlaviyoProperty, KlaviyoEvents, pushKlaviyoData } from '@/utils/klaviyoAnalytics';

import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';

import BookingFlowWrapper from './BookingFlow/BookingFlowWrapper';

const DocusignDialog = dynamic(() => import('./BookingFlow/DocusignDialog'), {
  ssr: false,
  loading: () => <BackdropLoader />,
});

type Props = {
  children: ReactNode;
  details: PropertyDetails;
};

const ListingDetailsWrapper = ({ children, details }: Props) => {
  const hasMounted = useHasMounted();
  const router = useRouter();
  const {
    step: bookingStep,
    setStep: setBookingStep,
    progressStatus,
    docusignUrl,
    setDocusignUrl,
  } = useBooking();
  const { profileData } = useProfile();

  const onCloseBookingFlow = useCallback(() => {
    setBookingStep(null);
  }, [setBookingStep]);

  useEffect(() => {
    if (hasMounted) {
      pushEcommerceDataLayer('view_item', [formatSeoProperty(details as any)]);
      pushKlaviyoData(
        KlaviyoEvents.LISTING_DETAIL_VIEWED,
        formatKlaviyoProperty(details as any, profileData?.nrUserId ?? ''),
      );
    }
  }, [details, hasMounted, profileData?.nrUserId]);

  const onComplete = useCallback(
    (bookingId: number, propertyId: number, event: string) => {
      setDocusignUrl('');
      router.push(
        `/request-to-book/approval?bookingId=${bookingId}&propertyId=${propertyId}&event=${event}`,
      );
    },
    [router, setDocusignUrl],
  );

  return (
    <>
      {children}{' '}
      {!!bookingStep && (
        <ResponsiveDialog
          open
          onOpenChange={onCloseBookingFlow}
          dialogClassName="p-0 md:h-auto md:w-[480px]"
          drawerClassName="overflow-y-hidden"
          drawerContentClassName="!p-0"
          hideCloseButton
        >
          <BookingFlowWrapper propertyDetails={details} />
        </ResponsiveDialog>
      )}
      {progressStatus === ProgressStatus.LOADING && !docusignUrl && (
        <ProgressDialog
          open={progressStatus === ProgressStatus.LOADING && !docusignUrl}
          title={'Preparing Rental Agreement ...'}
        />
      )}
      {progressStatus === ProgressStatus.SUCCESSFUL && docusignUrl && docusignUrl?.length > 0 && (
        <DocusignDialog agreementUrl={docusignUrl} onComplete={onComplete} />
      )}
    </>
  );
};

export default ListingDetailsWrapper;
