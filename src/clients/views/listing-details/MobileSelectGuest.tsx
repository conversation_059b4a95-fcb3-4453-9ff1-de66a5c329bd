'use client';

import { memo, useCallback, useEffect, useMemo } from 'react';

import { MinusIcon, PlusIcon, XCircleIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useBooking } from '@/contexts/BookingContext';
import { PetType, PropertyDetails } from '@/types/properties';
import FormHelperText from '@/ui/atoms/FormHelperText';
import InputLabel from '@/ui/atoms/InputLabel';
import { getStringSingularPlural } from '@/utils/common';

import classNames from 'classnames';

type Props = {
  petsAllowed?: boolean;
  property: PropertyDetails;
  capacity?: number;
  setGuestValuesValid?: (value: boolean) => void;
  onToggle: () => void;
};

const MobileSelectGuest = ({
  petsAllowed,
  property,
  capacity,
  setGuestValuesValid,
  onToggle,
}: Props) => {
  const {
    guests,
    setGuests,
    petCount,
    setPetCount,
    isPetSelected,
    setIsPetSelected,
    petType,
    setPetType,
    petDescription,
    setPetDescription,
  } = useBooking();

  const showGuestError = useMemo(() => capacity && guests.adults + guests.children > capacity, [
    capacity,
    guests.adults,
    guests.children,
  ]);

  const handleIncrementAdults = useCallback(() => {
    if ((capacity && guests.adults < capacity) || !capacity) {
      setGuests({
        ...guests,
        adults: guests.adults + 1,
      });
    }
  }, [capacity, guests, setGuests]);

  const handleDecrementAdults = useCallback(() => {
    if (guests.adults > 0) {
      setGuests({
        ...guests,
        adults: guests.adults - 1,
      });
    }
  }, [guests, setGuests]);

  const handleIncrementChildren = useCallback(() => {
    if ((capacity && guests.children < capacity) || !capacity) {
      setGuests({
        ...guests,
        children: guests.children + 1,
      });
    }
  }, [capacity, guests, setGuests]);

  const handleDecrementChildren = useCallback(() => {
    if (guests.children > 0) {
      setGuests({
        ...guests,
        children: guests.children - 1,
      });
    }
  }, [guests, setGuests]);

  const handleIncrementPetCount = useCallback(() => {
    setPetCount(petCount + 1);
  }, [petCount, setPetCount]);

  const handleDecrementPetCount = useCallback(() => {
    if (petCount > 1) {
      setPetCount(petCount - 1);
    }
  }, [petCount, setPetCount]);

  const onPetTypeChange = useCallback(
    (type: any) => {
      setPetType(type);
    },
    [setPetType],
  );

  const changePetDesc = useCallback(
    (event: any) => {
      const { value } = event.target;

      setPetDescription(value);
    },
    [setPetDescription],
  );

  const handlePetChange = useCallback(
    (value: string) => {
      console.log('value', value);
      setIsPetSelected(value === 'yes');
    },
    [setIsPetSelected],
  );

  useEffect(() => {
    if (capacity && guests.adults + guests.children > capacity) {
      setGuestValuesValid?.(false);
    } else {
      setGuestValuesValid?.(true);
    }
  }, [capacity, guests, setGuestValuesValid]);
  return (
    <div className="p-4 bg-white rounded-t-[10px] border border-solid border-[#E5E5E5]">
      <div className="flex items-start justify-between">
        <p className="m-0 text-xl font-medium">Guests</p>
        <Button onClick={onToggle} intent="ghost" className="!p-0">
          <XCircleIcon className="w-6 h-6" />
        </Button>
      </div>
      <div className="border border-solid border-platinium p-3.5 text-sm  text-metal-gray rounded-2xl mt-4">
        <div
          className="relative cursor-pointer flex items-center"
          onClick={onToggle}
          role="presentation"
        >
          {getStringSingularPlural('Adult', 'Adults', guests.adults)},{' '}
          {getStringSingularPlural('Child', 'Children', guests.children)}
          {isPetSelected && petCount > 0 && `, ${getStringSingularPlural('Pet', 'Pets', petCount)}`}
        </div>
        <Separator className="my-4" />
        <div className="flex items-center justify-between text-sm text-black mb-2">
          Adults
          <div className="flex items-center justify-between text-metal-gray font-medium w-[126px]">
            <span
              className={classNames(
                'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer ',
                {
                  '!cursor-no-drop text-disabled': guests.adults === 1,
                },
              )}
              role="button"
              onClick={handleDecrementAdults}
              onKeyDown={handleDecrementAdults}
              tabIndex={0}
            >
              <MinusIcon className="w-4 h-4" />
            </span>
            <span className="w-5 text-center">{guests.adults}</span>
            <span
              className={classNames(
                'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer ',
                {
                  '!cursor-no-drop text-disabled': guests.adults === capacity,
                },
              )}
              role="button"
              onClick={handleIncrementAdults}
              onKeyDown={handleIncrementAdults}
              tabIndex={0}
            >
              <PlusIcon className="w-4 h-4" />
            </span>
          </div>
        </div>
        <div className="flex items-center justify-between text-sm text-black">
          Children ( Ages 0 to 17 )
          <div className="flex items-center justify-between text-metal-gray font-medium w-[126px]">
            <span
              className={classNames(
                'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer ',
                {
                  '!cursor-no-drop text-disabled': guests.children === 0,
                },
              )}
              role="button"
              onClick={handleDecrementChildren}
              onKeyDown={handleDecrementChildren}
              tabIndex={0}
            >
              <MinusIcon className="w-4 h-4" />
            </span>
            <span className="w-5 text-center">{guests.children}</span>
            <span
              className={classNames(
                'h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer ',
                {
                  '!cursor-no-drop text-disabled': guests.children === capacity,
                },
              )}
              role="button"
              onClick={handleIncrementChildren}
              onKeyDown={handleIncrementChildren}
              tabIndex={0}
            >
              <PlusIcon className="w-4 h-4" />
            </span>
          </div>
        </div>
        {petsAllowed && (
          <div className="flex items-center justify-between text-sm text-black mt-2">
            Bringing a pet?
            <Select value={isPetSelected ? 'yes' : 'no'} onValueChange={handlePetChange}>
              <SelectTrigger className="w-[126px] rounded-[40px]">
                <SelectValue placeholder="" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="yes">Yes</SelectItem>
                <SelectItem value="no">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        {isPetSelected && (
          <>
            <div className="flex items-center justify-between text-sm text-black my-4">
              <Select value={petType} onValueChange={onPetTypeChange}>
                <SelectTrigger className="w-[126px] rounded-[40px]">
                  <SelectValue placeholder="" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={PetType.DOG}>Dog</SelectItem>
                  <SelectItem value={PetType.CAT}>Cat</SelectItem>
                  <SelectItem value={PetType.OTHER}>Other</SelectItem>
                </SelectContent>
              </Select>
              <div className="flex items-center justify-between text-metal-gray font-medium w-[126px]">
                <span
                  role="button"
                  onClick={handleDecrementPetCount}
                  onKeyDown={handleDecrementPetCount}
                  tabIndex={0}
                  className="h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer"
                >
                  <MinusIcon className="w-4 h-4" />
                </span>
                <span className="w-5 text-center">{petCount}</span>
                <span
                  role="button"
                  onClick={handleIncrementPetCount}
                  onKeyDown={handleIncrementPetCount}
                  tabIndex={0}
                  className="h-8 w-8 flex items-center justify-center rounded-full border border-solid border-platinium cursor-pointer"
                >
                  <PlusIcon className="w-4 h-4" />
                </span>
              </div>
            </div>
            <InputLabel className="px-[14px]">
              ADDITIONAL INFORMATION (BREED, WEIGHT, ETC)
            </InputLabel>
            <Input
              value={petDescription}
              onChange={changePetDesc}
              className="text-black text-sm p-2.5 px-3.5 rounded-[40px]"
            />
          </>
        )}
      </div>
      <p className="m-0 mt-2 text-xs uppercase text-metal-gray">
        This property has a maximum of {property.totalCapacity} guests. <br />
        {!petsAllowed
          ? `Pets are not allowed.`
          : `Pets allowed with prior permission, fees may apply.`}
      </p>
      {showGuestError && (
        <FormHelperText className="pl-2.5" error>
          Maximum guests for this property is {capacity}
        </FormHelperText>
      )}
      <Button
        className="w-full rounded-[32px] mt-4"
        onClick={!showGuestError ? onToggle : undefined}
        disabled={!!showGuestError}
      >
        Save
      </Button>
    </div>
  );
};

export default memo(MobileSelectGuest);
