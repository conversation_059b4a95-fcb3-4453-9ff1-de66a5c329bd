'use client';

import { useCallback, useEffect, useMemo, useRef, useState, memo } from 'react';

import { CalendarIcon, ChatBubbleOvalLeftIcon } from '@heroicons/react/24/outline';
import toast from 'react-hot-toast';

import Button from '@/clients/ui/Button';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { Skeleton } from '@/components/ui/skeleton';
import { MOBILE_CHECKOUT_CALENDAR_WRAPPER } from '@/constants/common';
import { BookingFlowStep, useBooking } from '@/contexts/BookingContext';
import { checkBookingAvailability } from '@/services/server/booking';
import { BookingAvailabilityData } from '@/types/booking';
import { PropertyDetails } from '@/types/properties';
import { buildBookingUrl, formatBookingFeesData } from '@/utils/booking';
import { currencyFormatterRound } from '@/utils/common';
import { formatSeoProperty, pushEcommerceDataLayer } from '@/utils/enhancedEcomAnanalytics';
import {
  BOOKING_REQUESTED,
  formatAmenities,
  formatKlavioBookingRequestedEventPayload,
  getPropertyPic,
  KlaviyoEvents,
  pushKlaviyoData,
} from '@/utils/klaviyoAnalytics';

import classNames from 'classnames';
import { differenceInCalendarDays, format } from 'date-fns';
import dynamic from 'next/dynamic';

import AskQuestionButton from './Inquire/InquireButton';
import MobileBottomDrawer from './MobileBottomDrawer';

const CheckoutDateRangePickerMobile = dynamic(
  () => import('./CheckoutDateRangePicker/CheckoutDateRangePickerMobile'),
  {
    loading: () => (
      <div className="fixed bg-black/40 inset-0 flex items-center justify-center text-white">
        <LoadingSpinner className="w-10 h-10" />
      </div>
    ),
  },
);

const MobilePricesAndSummary = dynamic(() => import('./MobilePricesAndSummary'), {
  loading: () => (
    <div className="fixed bg-black/40 inset-0 flex items-center justify-center text-white">
      <LoadingSpinner className="w-10 h-10" />
    </div>
  ),
});

type Props = {
  data: PropertyDetails;
};

const MobileCheckAvaialability = ({ data }: Props) => {
  const petsAllowed = useMemo(
    () => !!data.houseRules.find((_h) => _h.name === 'Pets Considered')?.activeFlag,
    [data.houseRules],
  );
  const [isFetchingBookingDetails, setIsFetchingBookingDetails] = useState<boolean>(false);
  const [openDatepicker, setOpenDatepicker] = useState<boolean>(false);
  const [openSummary, setOpenSummary] = useState<boolean>(false);
  const {
    date,
    setDate,
    guests,
    bookingAvailabilityData,
    setBookingAvailabilityData,
    petCount,
    petType,
    petDescription,
    isPetSelected,
    setStep,
  } = useBooking();
  const dateRef = useRef(date);
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );
  const bookingData = useMemo(
    () =>
      bookingAvailabilityData && formatBookingFeesData(bookingAvailabilityData, '', numberOfNights),
    [bookingAvailabilityData, numberOfNights],
  );

  const onToggleDatePicker = useCallback(() => {
    setOpenDatepicker((prev) => !prev);
  }, []);

  const onToggleSummaryDialog = useCallback(() => {
    setOpenSummary(!openSummary);
  }, [openSummary]);

  const onRequestToBook = useCallback(() => {
    if (!date?.from || !date?.to) {
      return;
    }
    if (isFetchingBookingDetails || !bookingAvailabilityData) {
      return;
    }

    setIsFetchingBookingDetails(true);

    const bookingUrlPath = buildBookingUrl({
      slug: data.slug,
      adults: guests.adults,
      children: guests.children,
      from: date.from,
      to: date.to,
      petCount: petsAllowed && isPetSelected ? petCount : undefined,
      petType: petsAllowed && isPetSelected ? petType : undefined,
      petDescription: petsAllowed && isPetSelected ? petDescription : undefined,
    });

    const klaviyoBooking = formatKlavioBookingRequestedEventPayload({
      from: format(date.from, 'yyyy-MM-dd'),
      to: format(date.to, 'yyyy-MM-dd'),
      total: bookingAvailabilityData.total,
      adults: guests.adults,
      children: guests.children,
      guests: guests.adults + guests.children,
      description: bookingAvailabilityData?.description ?? '',
      propertyId: data.nrPropertyId,
      neighborhood: data.neighborhood,
      totalBedrooms: data.totalBedrooms,
      totalCapacity: data.totalCapacity,
      amenities: formatAmenities(
        data.outdoorAmenities,
        data.laundryAmenities,
        data.entertainment,
        data.essentials,
      ),
      headline: data.headline,
      propertyImage: getPropertyPic(data.nrPropertyPics),
      bookingPageUrl: window.location.origin + bookingUrlPath,
    });

    pushEcommerceDataLayer('add_to_cart', [formatSeoProperty(data as any)], true);

    localStorage.setItem(BOOKING_REQUESTED, JSON.stringify(klaviyoBooking));
    pushKlaviyoData(KlaviyoEvents.BOOKING_REQUESTED_STARTED, klaviyoBooking);

    setStep(BookingFlowStep.DETAILS);
    setIsFetchingBookingDetails(false);
  }, [
    bookingAvailabilityData,
    data,
    date?.from,
    date?.to,
    guests.adults,
    guests.children,
    isFetchingBookingDetails,
    petCount,
    petDescription,
    petType,
    petsAllowed,
    isPetSelected,
    setStep,
  ]);

  const onSubmit = useCallback(() => {
    if (bookingAvailabilityData) {
      onRequestToBook();
    } else {
      onToggleDatePicker();
    }
  }, [bookingAvailabilityData, onRequestToBook, onToggleDatePicker]);

  useEffect(() => {
    dateRef.current = date;
    async function fetchDetails() {
      if (date?.from && date?.to) {
        setIsFetchingBookingDetails(true);
        try {
          const _data = await checkBookingAvailability<BookingAvailabilityData>(
            data.nrPropertyId,
            1,
            0,
            format(date.from, 'yyyy-MM-dd'),
            format(date.to, 'yyyy-MM-dd'),
          );
          if (!_data.rent || !_data.total) {
            toast.error((_data as any)?.details ?? 'Not available for given dates.');
            setBookingAvailabilityData(null);
            setIsFetchingBookingDetails(false);
            return;
          }

          if (dateRef.current?.from && dateRef.current?.to) {
            setBookingAvailabilityData(_data);
            setIsFetchingBookingDetails(false);
          }
        } catch (error) {
          console.error(error);
          setBookingAvailabilityData(null);
          setIsFetchingBookingDetails(false);
        }
      } else {
        setBookingAvailabilityData(null);
        setIsFetchingBookingDetails(false);
      }
    }
    fetchDetails();
  }, [date, data.nrPropertyId, setBookingAvailabilityData]);

  return (
    <>
      <div className={classNames({ 'pb-2': bookingAvailabilityData })}>
        <div className="mb-5">
          {!bookingAvailabilityData ? (
            <>
              <p className="m-0 text-base font-medium">
                {currencyFormatterRound.format(data?.averageNightlyRate ?? 0)}/night
              </p>
            </>
          ) : (
            <div className="flex items-center justify-between">
              {isFetchingBookingDetails ? (
                <>
                  <Skeleton className="w-[120px] h-4" />
                  <Skeleton className="w-[120px] h-4" />{' '}
                </>
              ) : (
                <>
                  <p
                    onClick={onToggleSummaryDialog}
                    role="presentation"
                    className="m-0 text-base font-medium underline w-[50%]"
                  >
                    {currencyFormatterRound.format(bookingData?.grandTotal ?? 0)}
                  </p>
                  <p
                    onClick={onToggleDatePicker}
                    role="presentation"
                    className="m-0 text-base font-medium underline w-[50%] truncate text-right"
                  >
                    {`${date?.from && format(date?.from, 'MMM d')} to ${
                      date?.to && format(date?.to, 'MMM d, yyyy')
                    }`}
                  </p>
                </>
              )}
            </div>
          )}
        </div>
        <div className="flex items-center justify-between gap-x-3">
          <Button
            onClick={onSubmit}
            className="rounded-full font-medium w-[calc(50%-6px)] py-4 flex items-center gap-x-2 h-[50px]"
          >
            <CalendarIcon className="w-5 h-5" />
            {bookingAvailabilityData ? `Reserve` : `Check Availability`}
          </Button>
          <AskQuestionButton
            listingDetails={data}
            title="Inquire"
            icon={<ChatBubbleOvalLeftIcon className="w-4 h-4" />}
            className="rounded-full border-solid !border-carolina-blue text-black font-medium w-[calc(50%-6px)] flex items-center gap-x-2 h-[50px]"
          />
        </div>
      </div>
      <MobileBottomDrawer open={openDatepicker} onToggle={onToggleDatePicker}>
        {openDatepicker && (
          <div id={MOBILE_CHECKOUT_CALENDAR_WRAPPER}>
            <CheckoutDateRangePickerMobile
              date={date}
              setDate={setDate}
              onClose={onToggleDatePicker}
              availableCalendar={data.availableCalendar.sort(
                (_a1, _a2) =>
                  new Date(_a1.blockedFrom).getTime() - new Date(_a2.blockedFrom).getTime(),
              )}
              bookingAvailabilityData={bookingAvailabilityData}
              rentalRates={data.rentalRates}
              isFetchingBookingDetails={isFetchingBookingDetails}
              propertyId={data.nrPropertyId}
            />
          </div>
        )}
      </MobileBottomDrawer>
      <MobileBottomDrawer open={openSummary} onToggle={onToggleSummaryDialog}>
        {openSummary && (
          <MobilePricesAndSummary
            onToggle={onToggleSummaryDialog}
            onToggleDatePicker={onToggleDatePicker}
            bookingData={bookingData}
            petsAllowed={petsAllowed}
            property={data}
            capacity={data.totalCapacity}
            numberOfNights={numberOfNights}
          />
        )}
      </MobileBottomDrawer>
    </>
  );
};

export default memo(MobileCheckAvaialability);
