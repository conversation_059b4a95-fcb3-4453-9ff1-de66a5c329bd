'use client';

import { useCallback, useState } from 'react';

import { PhoneIcon, XCircleIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';

import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import Button from '@/clients/ui/Button';
import Checkbox from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { EMAIL_PATTERN, PHONE_NUMBER_PATTERN } from '@/constants/patterns';
import useForm from '@/hooks/useForm';
import { submitBuyerInterest } from '@/services/server/booking';
import { Nullable, ProgressStatus } from '@/types/common';
import InputLabel from '@/ui/atoms/InputLabel';

import classNames from 'classnames';

type Props = {
  onToggle: () => void;
  listingId: Nullable<number>;
};

type BuyerInterestFormValues = {
  firstname: string;
  lastname: string;
  email: string;
  phone: string;
  budget_range: string | null;
  timeframe: string | null;
};

const BuyerInterestFormDialog = ({ onToggle, listingId }: Props) => {
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(null);
  const [sendSimilarHomes, setSendSimilarHomes] = useState<boolean>(false);
  const {
    formState,
    errors,
    onChange,
    preSubmitCheck: preSubmitCheck,
  } = useForm<BuyerInterestFormValues>(
    {
      firstname: '',
      lastname: '',
      email: '',
      phone: '',
      budget_range: null,
      timeframe: null,
    },
    {
      firstname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a first name`;
        }
      },
      lastname: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter a last name`;
        }
      },
      email: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter an email`;
        }

        if (!_value.match(EMAIL_PATTERN)) {
          return 'Invalid email address';
        }
      },
      phone: (_v, _n, _value: string) => {
        if (_value.trim().length === 0) {
          return `Please enter phone number`;
        }

        if (!_value.match(PHONE_NUMBER_PATTERN)) {
          return 'Invalid phone number';
        }
      },
    },
  );

  const onChangeTextInput = useCallback(
    (event: any) => {
      const { name, value } = event.target;

      onChange(value, name);
    },
    [onChange],
  );

  const onChangeCheckbox = useCallback(() => {
    setSendSimilarHomes((_f) => !_f);
  }, []);

  const handleTimeframeSelectChange = useCallback(
    (value: string) => {
      onChange(value, 'timeframe');
    },
    [onChange],
  );

  const handleBudgetRangeSelectChange = useCallback(
    (value: string) => {
      onChange(value, 'budget_range');
    },
    [onChange],
  );

  const onSubmit = useCallback(() => {
    const _errors = preSubmitCheck();
    if (Object.values(_errors).some((_error) => _error !== '')) {
      return;
    }
    setProgressStatus(ProgressStatus.LOADING);
    submitBuyerInterest({
      first_name: formState.firstname,
      last_name: formState.lastname,
      email: formState.email,
      phone: formState.phone,
      budget_range: formState?.budget_range ?? null,
      timeframe: formState?.timeframe ?? null,
      send_similar_homes: sendSimilarHomes,
      listing: listingId,
      source: 'nr',
    })
      .then((data) => {
        console.debug('the data is', data);
        setProgressStatus(ProgressStatus.SUCCESSFUL);
      })
      .catch((err) => {
        setProgressStatus(ProgressStatus.FAILED);
        toast(err);
      });
  }, [
    formState?.budget_range,
    formState.email,
    formState.firstname,
    formState.lastname,
    formState.phone,
    formState?.timeframe,
    listingId,
    preSubmitCheck,
    sendSimilarHomes,
  ]);

  return (
    <ResponsiveDialog
      open
      onOpenChange={onToggle}
      dialogClassName="p-0 md:h-auto md:w-[480px] gap-0"
      drawerClassName="overflow-y-hidden z-[66] bg-white"
      drawerContentClassName="!p-0"
      hideCloseButton
    >
      <div className=" bg-white">
        <XMarkIcon
          className="hidden md:block w-8 h-8 absolute -top-3 -right-3 bg-white rounded-full shadow-sm cursor-pointer p-2"
          onClick={onToggle}
        />
        <XCircleIcon className="md:hidden w-6 h-6 absolute top-4 right-4" onClick={onToggle} />
        {progressStatus === ProgressStatus.SUCCESSFUL ? (
          <div className="h-max p-4 pb-8">
            <p className="text-2xl m-0 font-bold italic tracking-[-0.6px] text-center text-[#18181B]">
              Thanks, you&apos;re all set!
            </p>
            <Separator className="my-2" />
            <p className="text-sm text-[#18181B] m-0">
              We&apos;ll let you know if this home ever comes on the market.
            </p>
            <br />
            <p className="text-xs text-[#18181B] m-0">
              If you&apos;d like to start exploring homes currently for sale, our sales team would
              be happy to share listings that match your preferences.
            </p>
            <p className="text-sm tracking-[-0.6px] font-semibold text-center leading-[32px] m-0">
              Contact our Sales Team
            </p>
            <a
              href="tel:************"
              className="flex items-center justify-center px-3 py-[5px] gap-x-2 rounded-full border border-solid border-carolina-blue shadow-sm no-underline text-[#0F172A] font-medium w-max mx-auto"
            >
              <PhoneIcon className="h-5 w-5" />
              ************
            </a>
          </div>
        ) : (
          <>
            <p className="p-4 text-2xl m-0 font-bold italic tracking-[-0.6px] text-center">
              Love This Home?
            </p>
            <Separator className="" />
            <div className="p-4 h-max max-h-[calc(90dvh-140px)] overflow-x-hidden overflow-y-scroll md:overflow-y-auto pb-5 md:pb-0">
              <p className="text-sm m-0">Be the first to know if it&apos;s ever listed for sale.</p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-4 my-4">
                <div>
                  <InputLabel
                    className={classNames(
                      'text-[#18181B]',
                      !!errors?.firstname?.length && 'text-error',
                    )}
                  >
                    First Name
                  </InputLabel>
                  <Input
                    type="text"
                    name="firstname"
                    value={formState.firstname}
                    placeholder="Enter your first name"
                    className="w-full rounded-lg"
                    onChange={onChangeTextInput}
                    helperText={errors?.firstname ?? ''}
                    error={!!errors?.firstname?.length}
                  />
                </div>
                <div>
                  <InputLabel
                    className={classNames(
                      'text-[#18181B]',
                      !!errors?.lastname?.length && 'text-error',
                    )}
                  >
                    Last Name
                  </InputLabel>
                  <Input
                    type="text"
                    name="lastname"
                    value={formState.lastname}
                    placeholder="Enter your last name"
                    className="w-full rounded-lg"
                    onChange={onChangeTextInput}
                    helperText={errors?.lastname ?? ''}
                    error={!!errors?.lastname?.length}
                  />
                </div>
              </div>
              <div className="mb-4">
                <InputLabel
                  className={classNames('text-[#18181B]', !!errors?.phone?.length && 'text-error')}
                >
                  Phone
                </InputLabel>
                <Input
                  type="tel"
                  name="phone"
                  value={formState.phone}
                  placeholder="Enter your phone number"
                  className="w-full rounded-lg"
                  onChange={onChangeTextInput}
                  helperText={errors?.phone ?? ''}
                  error={!!errors?.phone?.length}
                />
                <p className="text-xs m-0 mt-2 text-[#71717A]">
                  We&apos;ll text you if this home becomes available.
                </p>
              </div>
              <div className="mb-4">
                <InputLabel
                  className={classNames('text-[#18181B]', !!errors?.email?.length && 'text-error')}
                >
                  Email
                </InputLabel>
                <Input
                  type="email"
                  name="email"
                  value={formState.email}
                  placeholder="Enter your email"
                  className="w-full rounded-lg"
                  onChange={onChangeTextInput}
                  helperText={errors?.email ?? ''}
                  error={!!errors?.email?.length}
                />
                <div className="flex items-center w-full space-x-2 mt-2">
                  <Checkbox
                    className="w-4 h-4"
                    checked={sendSimilarHomes}
                    onChange={onChangeCheckbox}
                  />
                  <label
                    htmlFor="sendSimilarHomes"
                    className="cursor-pointer text-xs text-[#71717A]"
                  >
                    Send me similar homes nearby for sale
                  </label>
                </div>
              </div>
              <div className="mb-4">
                <InputLabel
                  className={classNames(
                    'text-[#18181B]',
                    !!errors?.timeframe?.length && 'text-error',
                  )}
                >
                  Are you considering buying in the next...
                </InputLabel>
                <Select
                  value={formState.timeframe ?? ''}
                  onValueChange={handleTimeframeSelectChange}
                >
                  <SelectTrigger className="w-full rounded-full">
                    <SelectValue placeholder="Select timeframe (optional)" />
                  </SelectTrigger>
                  <SelectContent className="z-[9999]">
                    <SelectItem value="within_12_months">Within 12 months</SelectItem>
                    <SelectItem value="12_24_months">12-24 months</SelectItem>
                    <SelectItem value="someday">Someday but not soon</SelectItem>
                    <SelectItem value="not_looking">Not looking to buy</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <InputLabel
                  className={classNames(
                    'text-[#18181B]',
                    !!errors?.budget_range?.length && 'text-error',
                  )}
                >
                  What&apos;s your general budget range?
                </InputLabel>
                <Select
                  value={formState.budget_range ?? ''}
                  onValueChange={handleBudgetRangeSelectChange}
                >
                  <SelectTrigger className="w-full rounded-full">
                    <SelectValue placeholder="Select budget range (optional)" />
                  </SelectTrigger>
                  <SelectContent className="z-[9999]">
                    <SelectItem value="no_pref">No preference / Rather not say</SelectItem>
                    <SelectItem value="under_2m">Under $2M</SelectItem>
                    <SelectItem value="2_4m">$2M - $4M</SelectItem>
                    <SelectItem value="4_6m">$4M - $6M</SelectItem>
                    <SelectItem value="6_10m">$6M - $10M</SelectItem>
                    <SelectItem value="10m_plus">Over $10M</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="p-4">
              <Button
                className="w-full rounded-full"
                onClick={onSubmit}
                disabled={progressStatus === ProgressStatus.LOADING}
                isLoading={progressStatus === ProgressStatus.LOADING}
              >
                Keep Me Posted
              </Button>
            </div>
          </>
        )}
      </div>
    </ResponsiveDialog>
  );
};

export default BuyerInterestFormDialog;
