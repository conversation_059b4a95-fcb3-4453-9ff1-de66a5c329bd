'use client';

import { ReactNode, useCallback, useState } from 'react';

import Button from '@/clients/ui/Button';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { PropertyPic } from '@/types/properties';

import dynamic from 'next/dynamic';
import { twMerge } from 'tailwind-merge';

const ViewAllPhotosDialog = dynamic(() => import('./ViewAllPhotosDialog'), {
  loading: () => (
    <div className="fixed bg-black/50 inset-0 flex items-center justify-center text-white">
      <LoadingSpinner className="w-10 h-10" />
    </div>
  ),
});

type Props = {
  children: ReactNode;
  nrPropertyId: number;
  defaultData: PropertyPic[];
  className?: string;
};

const ViewAllPhotos = ({ children, nrPropertyId, defaultData, className }: Props) => {
  const [showAll, setShowAll] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShowAll(!showAll);
  }, [showAll]);
  return (
    <>
      <Button className={twMerge('font-normal !p-0', className)} intent="ghost" onClick={onToggle}>
        {children}
      </Button>
      {showAll && (
        <ViewAllPhotosDialog
          open={showAll}
          onToggle={onToggle}
          nrPropertyId={nrPropertyId}
          defaultData={defaultData}
        />
      )}
    </>
  );
};

export default ViewAllPhotos;
