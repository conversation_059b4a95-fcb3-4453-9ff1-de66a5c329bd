'use client';

import { memo, useMemo } from 'react';

import { QuestionMarkCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';

import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import Button from '@/clients/ui/Button';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useBooking } from '@/contexts/BookingContext';
import { currencyFormatterRound } from '@/utils/common';
import {
  getBookingCalculatedValues,
  getDiscountName,
  getRentDayWiseBreakupString,
} from '@/utils/requestbook';

type Props = {
  open: boolean;
  onToggle: () => void;
};

const PriceBreakdownDialog = memo(({ open, onToggle }: Props) => {
  const { bookingAvailabilityData, isPetSelected, date, isInsuranceAdded } = useBooking();
  const { occupancyTax, grandTotal } = useMemo(
    () => getBookingCalculatedValues(bookingAvailabilityData, isPetSelected),
    [bookingAvailabilityData, isPetSelected],
  );
  const priceWiseDayString = useMemo(
    () =>
      date?.from &&
      date?.to &&
      getRentDayWiseBreakupString(
        date.from,
        date.to,
        bookingAvailabilityData?.rent ?? 0,
        bookingAvailabilityData?.rule_based_discount ?? 0,
      ),
    [
      bookingAvailabilityData?.rent,
      bookingAvailabilityData?.rule_based_discount,
      date?.from,
      date?.to,
    ],
  );

  if (!bookingAvailabilityData) {
    return null;
  }

  return (
    <ResponsiveDialog
      open={open}
      onOpenChange={onToggle}
      dialogClassName="p-4 w-[80vw] max-w-md mx-auto h-min max-h-[80dvh] rounded-xl"
      drawerContentClassName="pb-4"
    >
      <div className="relative">
        <Button onClick={onToggle} intent="ghost" className="!p-0 absolute top-0 right-0 md:hidden">
          <XCircleIcon className="w-6 h-6" />
        </Button>

        <p className="m-0 font-semibold">Price breakdown</p>
        <Separator className="my-2" />

        <div className="overflow-auto max-h-[70dvh] md:max-h-none pb-4">
          <div className="flex items-center justify-between mt-4">
            <p className="m-0 text-xs font-medium">{priceWiseDayString}</p>
            <p className="m-0 text-xs font-medium">
              {currencyFormatterRound.format(
                bookingAvailabilityData?.rent + (bookingAvailabilityData?.rule_based_discount ?? 0),
              )}
            </p>
          </div>

          {bookingAvailabilityData?.discount_type && bookingAvailabilityData?.rule_based_discount && (
            <div className="flex items-center justify-between mt-3">
              <p className="m-0 text-xs font-medium">
                {getDiscountName(bookingAvailabilityData?.discount_type ?? 'Discount Applied')}
              </p>
              <p className="m-0 text-xs font-medium text-green-600">
                -{currencyFormatterRound.format(bookingAvailabilityData?.rule_based_discount ?? 0)}
              </p>
            </div>
          )}

          <div className="flex items-center justify-between mt-3">
            <p className="m-0 text-xs font-medium flex items-center gap-x-1">
              Nantucket Rentals Service Fee
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild className="cursor-pointer">
                    <QuestionMarkCircleIcon className="w-4 h-4" />
                  </TooltipTrigger>
                  <TooltipContent className="max-w-[80dvw] md:max-w-[400px] z-[9999]">
                    <p>
                      This fee helps Nantucket Rentals provide secure booking experiences, and offer
                      24/7 local support throughout your trip. This fee is also less than VRBO and
                      AIRBNB.
                    </p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </p>
            <p className="m-0 text-xs font-medium">
              {currencyFormatterRound.format(bookingAvailabilityData.nantucket_fee ?? 0)}
            </p>
          </div>

          <div className="flex items-center justify-between mt-3">
            <p className="m-0 text-xs font-medium">Taxes</p>
            <p className="m-0 text-xs font-medium">
              {bookingAvailabilityData.occupancy_tax == 0
                ? 0
                : currencyFormatterRound.format(occupancyTax ?? 0)}
            </p>
          </div>

          <div className="flex items-center justify-between mt-3">
            <p className="m-0 text-xs">Other Fees</p>
            <p className="m-0 text-xs">
              {currencyFormatterRound.format(bookingAvailabilityData.other_fees ?? 0)}
            </p>
          </div>

          {isPetSelected && (
            <div className="flex items-center justify-between mt-3">
              <p className="m-0 text-xs">Pet Fee</p>
              <p className="m-0 text-xs">
                {currencyFormatterRound.format(bookingAvailabilityData.pet_fee ?? 0)}
              </p>
            </div>
          )}

          {isInsuranceAdded && (
            <div className="flex items-center justify-between mt-3">
              <p className="m-0 text-xs">Travel Insurance</p>
              <p className="m-0 text-xs">
                {currencyFormatterRound.format(
                  bookingAvailabilityData.travel_insurance_amount ?? 0,
                )}
              </p>
            </div>
          )}

          <Separator className="my-2" />

          <div className="flex items-center justify-between mt-3">
            <p className="m-0 text-sm font-semibold">TOTAL (USD)</p>
            <p className="m-0 text-sm font-semibold">
              {currencyFormatterRound.format(
                Number(grandTotal ?? 0) +
                  Number(
                    isInsuranceAdded ? bookingAvailabilityData?.travel_insurance_amount ?? 0 : 0,
                  ),
              )}
            </p>
          </div>
        </div>
      </div>
    </ResponsiveDialog>
  );
});

export default PriceBreakdownDialog;
