'use client';

import { ComponentProps, useCallback, useState } from 'react';

import Button from '@/clients/ui/Button';
import { PropertyDetails } from '@/types/properties';

import dynamic from 'next/dynamic';

const EditTripDetailsDialog = dynamic(() => import('./EditTripDetailsDialog'), { ssr: false });

type Props = {
  data: PropertyDetails;
  text?: string;
  defaultTab?: 'dates' | 'guests';
} & ComponentProps<typeof Button>;

const EditTripDetailsButton = ({ data, text, defaultTab, ...rest }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setOpen((_o) => !_o);
  }, []);

  return (
    <>
      <Button
        onClick={onToggle as any}
        intent="ghost"
        className="text-primary-text text-sm underline !p-0 font-semibold"
        {...rest}
      >
        {text ?? 'Edit'}
      </Button>
      {open && (
        <EditTripDetailsDialog
          onToggle={onToggle}
          data={data}
          defaultTab={defaultTab}
          petsAllowed={!!data.houseRules.find((_h) => _h.name === 'Pets Considered')?.activeFlag}
        />
      )}
    </>
  );
};

export default EditTripDetailsButton;
