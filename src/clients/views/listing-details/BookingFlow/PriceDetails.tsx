'use client';

import { memo, useCallback, useMemo, useState } from 'react';

import { InformationCircleIcon } from '@heroicons/react/24/outline';

import Button from '@/clients/ui/Button';
import { Separator } from '@/components/ui/separator';
import { useBooking } from '@/contexts/BookingContext';
import { currencyFormatterRound } from '@/utils/common';
import { getBookingCalculatedValues } from '@/utils/requestbook';

import { differenceInCalendarDays } from 'date-fns';
import dynamic from 'next/dynamic';

const PriceBreakdownDialog = dynamic(() => import('./PriceBreakdownDialog'), { ssr: false });

type Props = {
  showDueToday?: boolean;
};

const PriceDetails = memo<Props>(({ showDueToday }) => {
  const { bookingAvailabilityData, isPetSelected, date, isInsuranceAdded } = useBooking();
  const { occupancyTax, totalWithoutTaxes, grandTotal } = useMemo(
    () => getBookingCalculatedValues(bookingAvailabilityData, isPetSelected),
    [bookingAvailabilityData, isPetSelected],
  );
  const [showPricesBreakdown, setShowPricesBreakdown] = useState<boolean>(false);
  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date?.to, date?.from) : 0),
    [date?.from, date?.to],
  );

  const onToggle = useCallback(() => {
    setShowPricesBreakdown((_s) => !_s);
  }, []);

  return (
    <>
      <div className="flex items-center justify-between mb-3">
        <p className="m-0 font-medium">Price Details</p>
        <Button intent="ghost" className="!p-0 text-xs font-medium" onClick={onToggle}>
          <InformationCircleIcon className="w-4 h-4 mr-2" />
          Price breakdown
        </Button>
      </div>
      <div className="flex items-center justify-between mt-2">
        <p className="m-0 text-xs">{numberOfNights} nights</p>
        <p className="m-0 text-xs">{currencyFormatterRound.format(totalWithoutTaxes ?? 0)}</p>
      </div>
      <div className="flex items-center justify-between mt-2">
        <p className="m-0 text-xs">Taxes</p>
        <p className="m-0 text-xs">{currencyFormatterRound.format(occupancyTax ?? 0)}</p>
      </div>
      <Separator className="my-4" />
      <div className="flex items-center justify-between mt-2">
        <p className="m-0 text-xs font-semibold">TOTAL</p>
        <p className="m-0 text-xs font-semibold">
          {currencyFormatterRound.format(
            Number(grandTotal ?? 0) +
              Number(isInsuranceAdded ? bookingAvailabilityData?.travel_insurance_amount ?? 0 : 0),
          )}
        </p>
      </div>
      <p className="text-[10px] text-grey-main m-0">
        Price to be confirmed by your rental specialist.
      </p>

      {showDueToday && (
        <>
          <div className="flex items-center justify-between mt-3">
            <p className="m-0 text-xs font-semibold">Deposit due today</p>
            <p className="m-0 text-xs font-semibold">
              {currencyFormatterRound.format(
                Number(bookingAvailabilityData?.payment_schedule?.[0][1] ?? 0),
              )}
            </p>
          </div>
          <p className="text-[10px] text-grey-main m-0">
            Payment will be processed only if the booking goes through.
          </p>
        </>
      )}
      {showPricesBreakdown && (
        <PriceBreakdownDialog open={showPricesBreakdown} onToggle={onToggle} />
      )}
    </>
  );
});

export default PriceDetails;
