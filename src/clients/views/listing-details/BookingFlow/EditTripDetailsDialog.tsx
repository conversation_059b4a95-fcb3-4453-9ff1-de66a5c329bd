'use client';

import { memo, useCallback, useEffect, useRef, useState } from 'react';

import { XCircleIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';
import toast from 'react-hot-toast';

import ResponsiveDialog from '@/clients/components/common/ResponsiveDialog';
import Button from '@/clients/ui/Button';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MOBILE_CHECKOUT_CALENDAR_WRAPPER } from '@/constants/common';
import { useBooking } from '@/contexts/BookingContext';
import { checkBookingAvailability } from '@/services/server/booking';
import { BookingAvailabilityData } from '@/types/booking';
import { Nullable } from '@/types/common';
import { PropertyDetails } from '@/types/properties';

import { format } from 'date-fns';

import CheckoutDateRangePicker from '../CheckoutDateRangePicker';
import CheckoutDateRangePickerMobile from '../CheckoutDateRangePicker/CheckoutDateRangePickerMobile';
import GuestSelectorComponent from '../GuestAndPetSelector/GuestSelectorComponent';

type Props = {
  onToggle: () => void;
  data: PropertyDetails;
  petsAllowed?: boolean;
  defaultTab?: 'dates' | 'guests';
};

const EditTripDetailsDialog = ({ onToggle, data, petsAllowed, defaultTab = 'dates' }: Props) => {
  const {
    date: initialDate,
    setDate: setDateInContext,
    bookingAvailabilityData: defaultAvailabilityData,
    guests,
    setGuests,
    petCount,
    setPetCount,
    petType,
    setPetType,
    petDescription,
    setPetDescription,
    isPetSelected,
    setIsPetSelected,
  } = useBooking();
  const [date, setDate] = useState<DateRange | undefined>(initialDate);
  const [isFetchingBookingDetails, setIsFetchingBookingDetails] = useState<boolean>(false);
  const [bookingAvailabilityData, setBookingAvailabilityData] = useState<
    Nullable<BookingAvailabilityData>
  >(defaultAvailabilityData);

  const onClear = useCallback(() => {
    setDate(undefined);
    setBookingAvailabilityData(null);
  }, [setDate, setBookingAvailabilityData]);

  const onSave = useCallback(() => {
    setDateInContext(date);
    onToggle();
  }, [date, onToggle, setDateInContext]);

  const dateRef = useRef(date);

  useEffect(() => {
    if (!date?.from || !date?.to) {
      setBookingAvailabilityData(null);
      setIsFetchingBookingDetails(false);
      return;
    }

    dateRef.current = date;

    const NOT_AVAILABLE_MSG = 'Not available for given dates.';

    const fetchBookingAvailability = async (from: Date, to: Date) => {
      setIsFetchingBookingDetails(true);

      try {
        const _data = await checkBookingAvailability<BookingAvailabilityData>(
          data.nrPropertyId,
          1,
          0,
          format(from, 'yyyy-MM-dd'),
          format(to, 'yyyy-MM-dd'),
        );

        if (!_data.rent || !_data.total) {
          const errorMsg = (_data as any)?.details ?? NOT_AVAILABLE_MSG;
          toast.error(errorMsg);
          setBookingAvailabilityData(null);
          setIsFetchingBookingDetails(false);
          return;
        }

        if (dateRef.current?.from && dateRef.current?.to) {
          setBookingAvailabilityData(_data);
          setIsFetchingBookingDetails(false);
        }
      } catch (error) {
        console.error(error);
        setBookingAvailabilityData(null);
        setIsFetchingBookingDetails(false);
      }
    };

    const handler = setTimeout(() => {
      if (date?.from && date?.to) {
        fetchBookingAvailability(date.from, date.to);
      }
    }, 500); // debounce

    return () => clearTimeout(handler);
  }, [data.nrPropertyId, date]);

  return (
    <ResponsiveDialog
      open={true}
      onOpenChange={onToggle}
      dialogClassName="md:min-w-[630px] md:max-w-max py-10 md:py-0"
      drawerClassName="h-[95dvh] overflow-y-hidden max-h-[95dvh]"
      drawerContentClassName="overflow-y-hidden"
      hideCloseButton
    >
      <div className="md:p-6">
        <Button onClick={onToggle} intent="ghost" className="!p-0 absolute top-4 right-4">
          <XCircleIcon className="w-6 h-6" />
        </Button>
        <p className="font-medium text-lg md:text-xl leading-[140%] tracking-[0.5px] m-0 text-center md:text-left">
          Change reservation details
        </p>
        <Tabs defaultValue={defaultTab} className="w-full min-h-[500px]">
          <TabsList className="w-full my-2 md:my-4">
            <TabsTrigger value="dates" className="w-full cursor-pointer">
              Dates
            </TabsTrigger>
            <TabsTrigger value="guests" className="w-full cursor-pointer">
              Guests
            </TabsTrigger>
          </TabsList>
          <TabsContent className="mt-0 md:mt-2" value="dates">
            <div className="hidden md:block">
              <CheckoutDateRangePicker
                date={date}
                onClear={onClear}
                onClose={onToggle}
                setDate={setDate}
                bookingAvailabilityData={bookingAvailabilityData}
                availableCalendar={data.availableCalendar}
                rentalRates={data.rentalRates}
                propertyId={data.nrPropertyId}
                isFetchingBookingDetails={isFetchingBookingDetails}
                showSave
                onSave={onSave}
              />
            </div>
            <div className="md:hidden" id={MOBILE_CHECKOUT_CALENDAR_WRAPPER}>
              <CheckoutDateRangePickerMobile
                date={date}
                setDate={setDate}
                onClose={onToggle}
                availableCalendar={data.availableCalendar.sort(
                  (_a1, _a2) =>
                    new Date(_a1.blockedFrom).getTime() - new Date(_a2.blockedFrom).getTime(),
                )}
                bookingAvailabilityData={bookingAvailabilityData}
                rentalRates={data.rentalRates}
                isFetchingBookingDetails={isFetchingBookingDetails}
                propertyId={data.nrPropertyId}
                className="p-0"
                calendarWrapperClassName="h-[44dvh]"
                onSave={onSave}
                showSave
                hideClose
              />
            </div>
          </TabsContent>
          <TabsContent className="mt-0 md:mt-2 h-[70dvh] md:h-auto" value="guests">
            <div className="h-full md:h-[400px] relative">
              <GuestSelectorComponent
                petsAllowed={petsAllowed}
                capacity={data.totalCapacity}
                guests={guests}
                setGuests={setGuests}
                petCount={petCount}
                setPetCount={setPetCount}
                petType={petType}
                setPetType={setPetType}
                petDescription={petDescription}
                setPetDescription={setPetDescription}
                isPetSelected={isPetSelected}
                setIsPetSelected={setIsPetSelected}
              />
              <p className="text-xs uppercase text-metal-gray">
                This property has a maximum of {data.totalCapacity} guests. <br />
                {!petsAllowed
                  ? `Pets are not allowed.`
                  : `Pets allowed with prior permission, fees may apply.`}
              </p>

              <div className="absolute w-full flex flex-col bottom-0">
                <Separator className="my-4" />
                <Button
                  intent="outline"
                  className="text-sm font-normal rounded-[32px] text-right w-min self-end"
                  onClick={onToggle}
                >
                  Close
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </ResponsiveDialog>
  );
};

export default memo(EditTripDetailsDialog);
