'use client';

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import toast from 'react-hot-toast';

import Button from '@/clients/ui/Button';
import { Separator } from '@/components/ui/separator';
import { BookingFlowStep, useBooking } from '@/contexts/BookingContext';
import { checkBookingAvailability } from '@/services/server/booking';
import { BookingAvailabilityData } from '@/types/booking';
import { Nullable, ProgressStatus } from '@/types/common';
import { PropertyDetails } from '@/types/properties';
import FormHelperText from '@/ui/atoms/FormHelperText';
import { formatBookingFeesData } from '@/utils/booking';
import { buildBookingUrl } from '@/utils/booking';
import { currencyFormatterRound } from '@/utils/common';
import { formatSeoProperty, pushEcommerceDataLayer } from '@/utils/enhancedEcomAnanalytics';
import {
  BOOKING_REQUESTED,
  formatAmenities,
  formatKlavioBookingRequestedEventPayload,
  getPropertyPic,
  KlaviyoEvents,
  pushKlaviyoData,
} from '@/utils/klaviyoAnalytics';

import { differenceInCalendarDays, format } from 'date-fns';

import CheckinCheckout from './CheckinCheckout';
import GuestAndPetSelector from './GuestAndPetSelector';
import AskQuestionButton from './Inquire/InquireButton';
import PricesAndSummary from './PricesAndSummary';

type Props = {
  petsAllowed?: boolean;
  data: PropertyDetails;
};

const RequestToBookForm = ({ petsAllowed, data }: Props) => {
  const [isGuestsValid, setGuestValuesValid] = useState<boolean>(true);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | undefined>(undefined);
  const {
    date,
    guests,
    bookingAvailabilityData,
    setBookingAvailabilityData,
    petCount,
    petType,
    petDescription,
    isPetSelected,
    setStep,
    setGuests,
    setIsPetSelected,
    setPetCount,
    setPetType,
    setPetDescription,
  } = useBooking();
  const [datePickerError, setDatePickerError] = useState<Nullable<string>>(null);

  const dateRef = useRef(date);

  const numberOfNights = useMemo(
    () => (date?.from && date?.to ? differenceInCalendarDays(date.to, date.from) : 0),
    [date?.from, date?.to],
  );

  const bookingData = useMemo(
    () =>
      bookingAvailabilityData && formatBookingFeesData(bookingAvailabilityData, '', numberOfNights),
    [bookingAvailabilityData, numberOfNights],
  );

  const onRequestToBook = useCallback(() => {
    if (!date?.from || !date?.to) {
      setDatePickerError('Please select dates');
      return;
    }
    if (progressStatus === ProgressStatus.LOADING || !isGuestsValid || !bookingAvailabilityData) {
      return;
    }

    setProgressStatus(ProgressStatus.LOADING);

    const bookingUrlPath = buildBookingUrl({
      slug: data.slug,
      adults: guests.adults,
      children: guests.children,
      from: date.from,
      to: date.to,
      petCount: petsAllowed && isPetSelected ? petCount : undefined,
      petType: petsAllowed && isPetSelected ? petType : undefined,
      petDescription: petsAllowed && isPetSelected ? petDescription : undefined,
    });

    const klaviyoBooking = formatKlavioBookingRequestedEventPayload({
      from: format(date.from, 'yyyy-MM-dd'),
      to: format(date.to, 'yyyy-MM-dd'),
      total: bookingAvailabilityData.total,
      adults: guests.adults,
      children: guests.children,
      guests: guests.adults + guests.children,
      description: bookingAvailabilityData?.description ?? '',
      propertyId: data.nrPropertyId,
      neighborhood: data.neighborhood,
      totalBedrooms: data.totalBedrooms,
      totalCapacity: data.totalCapacity,
      amenities: formatAmenities(
        data.outdoorAmenities,
        data.laundryAmenities,
        data.entertainment,
        data.essentials,
      ),
      headline: data.headline,
      propertyImage: getPropertyPic(data.nrPropertyPics),
      bookingPageUrl: window.location.origin + bookingUrlPath,
    });

    pushEcommerceDataLayer('add_to_cart', [formatSeoProperty(data as any)], true);

    localStorage.setItem(BOOKING_REQUESTED, JSON.stringify(klaviyoBooking));
    pushKlaviyoData(KlaviyoEvents.BOOKING_REQUESTED_STARTED, klaviyoBooking);

    setStep(BookingFlowStep.DETAILS);
    // router.push(bookingUrlPath);
    setProgressStatus(ProgressStatus.SUCCESSFUL);
  }, [
    date?.from,
    date?.to,
    progressStatus,
    isGuestsValid,
    bookingAvailabilityData,
    data,
    guests.adults,
    guests.children,
    petsAllowed,
    isPetSelected,
    petCount,
    petType,
    petDescription,
    setStep,
  ]);

  useEffect(() => {
    dateRef.current = date;
    async function fetchDetails() {
      if (date?.from && date?.to) {
        setDatePickerError(null);
        setProgressStatus(ProgressStatus.LOADING);
        try {
          const _data = await checkBookingAvailability<BookingAvailabilityData>(
            data.nrPropertyId,
            1,
            0,
            format(date.from, 'yyyy-MM-dd'),
            format(date.to, 'yyyy-MM-dd'),
          );
          if (!_data.rent || !_data.total) {
            toast.error((_data as any)?.details ?? 'Not available for given dates.');
            setDatePickerError((_data as any)?.details ?? 'Not available for given dates.');
            setBookingAvailabilityData(null);
            setProgressStatus(ProgressStatus.FAILED);
            return;
          }
          if (dateRef.current?.from && dateRef.current?.to) {
            setBookingAvailabilityData(_data);
            setProgressStatus(ProgressStatus.SUCCESSFUL);
          }
        } catch (error) {
          console.error(error);
          setBookingAvailabilityData(null);
          setProgressStatus(ProgressStatus.FAILED);
        }
      } else {
        setBookingAvailabilityData(null);
        setProgressStatus(undefined);
      }
    }
    fetchDetails();
  }, [date, data.nrPropertyId, setBookingAvailabilityData]);

  useEffect(() => {
    if (data.totalCapacity && guests.adults + guests.children > data.totalCapacity) {
      setGuestValuesValid(false);
    } else {
      setGuestValuesValid(true);
    }
  }, [data.totalCapacity, guests]);

  return (
    <>
      <p className="m-0 text-[50px] font-medium">
        {currencyFormatterRound.format(
          bookingData ? bookingData.averageNightlyRate ?? 0 : data?.averageNightlyRate ?? 0,
        )}
        <span className="text-base font-normal ml-2 !mb-3">Per Night</span>
      </p>
      <Separator className="mt-4" />
      <div className="my-8">
        <CheckinCheckout propertyDetails={data} propertyId={data.nrPropertyId} />
        {datePickerError && (
          <FormHelperText className="pl-2.5" error>
            {datePickerError}
          </FormHelperText>
        )}
        <div className="mt-6">
          <GuestAndPetSelector
            guestsValues={guests}
            setGuestsValues={setGuests}
            petCount={petCount}
            setPetCount={setPetCount}
            isPetSelected={isPetSelected}
            petsAllowed={petsAllowed}
            capacity={data.totalCapacity}
            petType={petType}
            setPetType={setPetType}
            petDescription={petDescription}
            setPetDescription={setPetDescription}
            setIsPetSelected={setIsPetSelected}
          />
          <p className="m-0 mt-2 text-xs uppercase text-metal-gray px-3.5">
            This property has a maximum of {data.totalCapacity} guests. <br />
            {!petsAllowed
              ? `Pets are not allowed.`
              : `Pets allowed with prior permission, fees may apply.`}
          </p>
        </div>
      </div>
      {!!bookingData && (
        <PricesAndSummary bookingData={bookingData} numberOfNights={numberOfNights} />
      )}
      <div className="flex items-center gap-x-3 my-4">
        <Button
          intent="primary"
          className="w-[50%] rounded-full py-4 font-medium border-none h-[54px]"
          disabled={progressStatus === ProgressStatus.LOADING}
          onClick={onRequestToBook}
        >
          {bookingAvailabilityData ? `Reserve` : `Check Availability`}
        </Button>
        <AskQuestionButton
          className="w-[50%] border border-solid rounded-full !border-carolina-blue text-black h-[54px]"
          listingDetails={data}
          title="Inquire"
        />
      </div>
    </>
  );
};

export default RequestToBookForm;
