'use client';

import { ReactNode, useCallback, useState } from 'react';

import Button from '@/clients/ui/Button';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { PropertyDetails } from '@/types/properties';

import dynamic from 'next/dynamic';
import { twMerge } from 'tailwind-merge';

const InquireDialog = dynamic(() => import('./InquireDialog'), {
  ssr: false,
  loading: () => (
    <div className="fixed bg-black/40 inset-0 flex items-center justify-center text-white">
      <LoadingSpinner className="w-10 h-10" />
    </div>
  ),
});

type Props = {
  listingDetails: PropertyDetails;
  className?: string;
  icon?: ReactNode;
  title?: string;
};

const InquireButton = ({ listingDetails, className = '', icon, title = 'Inquire' }: Props) => {
  const [show, setShow] = useState<boolean>(false);

  const onToggle = useCallback(() => {
    setShow((prev) => !prev);
  }, []);

  return (
    <>
      <Button
        intent="ghost"
        className={twMerge(
          'text-foundation-blue font-medium w-full py-4 hover:bg-transparent hover:text-carolina-blue-40',
          className,
        )}
        onClick={onToggle}
      >
        {icon}
        {title}
      </Button>
      {show && <InquireDialog listingDetails={listingDetails} onToggle={onToggle} />}
    </>
  );
};

export default InquireButton;
