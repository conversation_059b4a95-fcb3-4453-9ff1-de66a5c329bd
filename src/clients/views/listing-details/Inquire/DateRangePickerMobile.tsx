'use client';

import { useCallback, useEffect, useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';

import LoadingSpinner from '@/components/ui/loading-spinner';
import { MOBILE_CHECKOUT_CALENDAR_WRAPPER } from '@/constants/common';
import { PropertyDetails } from '@/types/properties';
import InputLabel from '@/ui/atoms/InputLabel';

import { format } from 'date-fns';
import dynamic from 'next/dynamic';
import { Drawer } from 'vaul';

const CheckoutDateRangePickerMobile = dynamic(
  () => import('../CheckoutDateRangePicker/CheckoutDateRangePickerMobile'),
  {
    loading: () => (
      <div className="md:min-w-[570px] md:min-h-[468px] flex items-center justify-center">
        <LoadingSpinner />
      </div>
    ),
  },
);

type Props = {
  date?: DateRange;
  setDate: (_d?: DateRange) => void;
  propertyDetails: PropertyDetails;
};

const DateRangePickerMobile = ({ date, setDate, propertyDetails }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const onClose = useCallback(() => {
    setOpen(false);
  }, []);

  const onToggleDatePicker = useCallback(() => {
    setOpen((prev) => !prev);
  }, []);

  const onClick = useCallback(() => {
    setOpen(true);
  }, []);

  useEffect(() => {
    if (date?.from && date?.to) {
      onClose();
    }
  }, [date?.from, date?.to, onClose]);

  return (
    <Drawer.Root direction="bottom" open={open} onOpenChange={setOpen}>
      <div className="flex items-center gap-x-2 md:hidden my-4">
        <div className="w-1/2">
          <InputLabel className="">CHECK IN</InputLabel>

          <Drawer.Trigger asChild>
            <div
              onClick={onClick}
              onKeyDown={onClick}
              role="button"
              tabIndex={0}
              className="border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer"
            >
              {date?.from ? format(date?.from, 'LLL d, yyyy') : 'Add Date'}
              <ChevronDownIcon className="w-4 h-4 text-foundation-blue absolute right-4" />
            </div>
          </Drawer.Trigger>
        </div>
        <div className="w-1/2">
          <InputLabel className="">CHECK OUT</InputLabel>
          <div
            onClick={onClick}
            onKeyDown={onClick}
            role="button"
            tabIndex={0}
            className="border border-solid border-platinium flex items-center gap-x-2 py-2.5 px-3.5 text-sm  text-metal-gray rounded-[40px] relative cursor-pointer"
          >
            {date?.to ? format(date?.to, 'LLL d, yyyy') : 'Add Date'}
            <ChevronDownIcon className="w-4 h-4 text-foundation-blue absolute right-4" />
          </div>
        </div>
      </div>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-[999]" />
        <Drawer.Content className="bg-gray-100 flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 outline-none z-[999]">
          <Drawer.Title className="hidden"></Drawer.Title>
          <Drawer.Description className="hidden">Hidden description</Drawer.Description>
          {open && (
            <div id={MOBILE_CHECKOUT_CALENDAR_WRAPPER}>
              <CheckoutDateRangePickerMobile
                className="shadow-sm"
                date={date}
                setDate={setDate}
                onClose={onToggleDatePicker}
                availableCalendar={propertyDetails.availableCalendar.sort(
                  (_a1, _a2) =>
                    new Date(_a1.blockedFrom).getTime() - new Date(_a2.blockedFrom).getTime(),
                )}
                bookingAvailabilityData={null}
                rentalRates={propertyDetails.rentalRates}
                propertyId={propertyDetails.nrPropertyId}
              />
            </div>
          )}
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};

export default DateRangePickerMobile;
