'use client';

import { useState } from 'react';

import { ChevronDownIcon } from '@heroicons/react/24/outline';

import { GuestsValues } from '@/clients/components/common/GuestSelector';
import LoadingSpinner from '@/components/ui/loading-spinner';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

import dynamic from 'next/dynamic';

const GuestorSelectorDropdownComponent = dynamic(
  () => import('@/clients/components/common/GuestSelector/GuestorSelectorDropdownComponent'),
  {
    loading: () => (
      <div className="w-full h-[100px] flex items-center justify-center">
        <LoadingSpinner />
      </div>
    ),
  },
);

type Props = {
  capacity?: number;
  guestsValues: GuestsValues;
  setGuestsValues: (guests: GuestsValues) => void;
  setGuestValuesValid?: (value: boolean) => void;
};

const GuestSelectorInput = ({ capacity, guestsValues, setGuestsValues }: Props) => {
  const [open, setOpen] = useState<boolean>(false);

  const handleClose = () => {
    setOpen(false);
  };

  const handleIncrement = (field: keyof GuestsValues) => {
    if ((capacity && guestsValues[field] < capacity) || !capacity) {
      setGuestsValues({
        ...guestsValues,
        [field]: guestsValues[field] + 1,
      });
    }
  };

  const handleDecrement = (field: keyof GuestsValues) => {
    if (guestsValues[field] > 0) {
      setGuestsValues({
        ...guestsValues,
        [field]: guestsValues[field] - 1,
      });
    }
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="flex items-center justify-between gap-x-2 py-[8.5px] px-[14px] text-sm text-metal-gray bg-white border border-solid border-platinium rounded-sm cursor-pointer">
            {`${(guestsValues?.adults ?? 1) + (guestsValues?.children ?? 0)} guests`}

            <ChevronDownIcon className={`w-4 h-4 text-platinum ${open && 'rotate-180'}`} />
          </div>
        </PopoverTrigger>
        <PopoverContent
          className="w-80 border border-solid border-english-manor border-opacity-40"
          align="start"
        >
          {open && (
            <GuestorSelectorDropdownComponent
              handleClose={handleClose}
              open={open}
              handleIncrement={handleIncrement}
              handleDecrement={handleDecrement}
              guestsValues={guestsValues}
              capacity={capacity}
            />
          )}
        </PopoverContent>
      </Popover>
    </>
  );
};

export default GuestSelectorInput;
