'use client';

import { memo, ReactNode } from 'react';

import { twMerge } from 'tailwind-merge';
import { Drawer } from 'vaul';

type Props = {
  open: boolean;
  onToggle: () => void;
  overlayClassName?: string;
  contentClassName?: string;
  children: ReactNode;
};

const MobileBottomDrawer = ({
  open,
  onToggle,
  children,
  overlayClassName = '',
  contentClassName = '',
}: Props) => {
  return (
    <Drawer.Root direction="bottom" open={open} onOpenChange={onToggle}>
      <Drawer.Portal>
        <Drawer.Overlay className={twMerge('fixed inset-0 bg-black/40', overlayClassName)} />
        <Drawer.Content
          className={twMerge(
            'bg-gray-100 flex flex-col rounded-t-[10px] mt-24 fixed bottom-0 left-0 right-0 outline-none z-[999]',
            contentClassName,
          )}
        >
          <Drawer.Title className="hidden"></Drawer.Title>
          <Drawer.Description className="hidden">Hidden description</Drawer.Description>
          {children}
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
};

export default memo(MobileBottomDrawer);
