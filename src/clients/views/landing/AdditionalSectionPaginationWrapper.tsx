'use client';

import uniqBy from 'lodash/uniqBy';
import { ReactNode, useCallback, useState } from 'react';

import PropertyCard from '@/app/components/common/PropertyCard';
import PropertyCardClientWrapper from '@/clients/components/common/PorpertyCardClientWrapper';
import Button from '@/clients/ui/Button';
import { searchPropertyListings } from '@/services/server/property-search';
import { IPagination } from '@/types/common';
import { NRProperties } from '@/types/properties';
import { parseDateString } from '@/utils/common';
import { getSearchParamsForAdditionalPropertyCard } from '@/utils/listing-search';

type Props = {
  children: ReactNode;
  queryParams?: { [key: string]: string };
  pagesCount?: number;
  initial: NRProperties[];
};

type Payload = { results: NRProperties[] } & IPagination & {
    queryParams: { [key: string]: string };
  };

const AdditionalSectionPaginationWrapper = ({
  children,
  queryParams = {},
  pagesCount,
  initial,
}: Props) => {
  const [page, setPage] = useState<number | undefined>(undefined);
  const [additionalListings, setAdditionalListings] = useState<NRProperties[]>(initial);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const onLoadMore = useCallback(() => {
    setPage((page ?? 1) + 1);
    setIsLoading(true);
    searchPropertyListings<Payload>(
      (page ?? 1) + 1,
      Object.entries(queryParams)
        .flatMap(([key, value]) => {
          if (key === 'page') {
            return [];
          }
          if (key === 'excludePropertyIds') {
            return `excludePropertyIds=${additionalListings
              .map((_a) => _a.nrPropertyId)
              .join(',')}`;
          }

          return `${key}=${value}`;
        })
        .join('&'),
    )
      .then((_data) => {
        setAdditionalListings(
          uniqBy([...additionalListings, ...(_data?.results ?? [])], 'nrPropertyId'),
        );
        setIsLoading(false);
      })
      .catch((_error) => {
        console.log('failed to fetch listings', _error);
        setIsLoading(false);
      });
  }, [additionalListings, page, queryParams]);

  return (
    <>
      {additionalListings && page ? (
        <>
          {
            <div className="grid grid-cols-1 md:grid-cols-3 2xl:grid-cols-4 gap-x-6 gap-y-8">
              {additionalListings?.map((_l) => (
                <PropertyCardClientWrapper key={_l.nrPropertyId} propertyDetails={_l}>
                  <PropertyCard
                    propertyDetails={_l}
                    className="!w-full"
                    showDateRange
                    searchParams={getSearchParamsForAdditionalPropertyCard(
                      queryParams,
                      parseDateString(_l.NrRentalRates[0].rateStartFrom),
                      _l.NrRentalRates[0].minimumNightsStay,
                    )}
                  />
                </PropertyCardClientWrapper>
              ))}
            </div>
          }
          {isLoading && (
            <div className="my-[100px] w-full flex items-center justify-center">
              <div className="loading loading-spinner loading-md" />
            </div>
          )}
        </>
      ) : (
        children
      )}
      {pagesCount && pagesCount > 1 && !isLoading && (page ?? 1) + 1 < pagesCount && (
        <div className="mt-10 flex items-center justify-center">
          <Button onClick={onLoadMore}>Load more</Button>
        </div>
      )}
    </>
  );
};

export default AdditionalSectionPaginationWrapper;
