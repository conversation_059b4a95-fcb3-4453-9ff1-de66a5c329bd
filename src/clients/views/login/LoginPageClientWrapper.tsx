'use client';

import { ReactNode, useEffect } from 'react';

import { useLoginDialog, useProfile } from '@/contexts/selectors/app-context-selectors';

import { useRouter } from 'next/navigation';

type Props = {
  children: ReactNode;
};

const LoginPageClientWrapper = ({ children }: Props) => {
  const router = useRouter();
  const { onToggleLoginDialog } = useLoginDialog();
  const { profileData } = useProfile();

  useEffect(() => {
    const timer = setTimeout(() => {
      if (profileData) {
        router.replace('/');
      } else {
        onToggleLoginDialog();
      }
    }, 300);

    return () => {
      clearTimeout(timer);
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [profileData]);

  return <>{children}</>;
};

export default LoginPageClientWrapper;
