import { getAllListingPhotos } from '@/services/server/properties';
import { PropertyPic } from '@/types/properties';

import useSWR from 'swr';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

function useListingPhotos(nrPropertyId: number, initial: PropertyPic[]) {
  const { data = [], isLoading, error, isValidating } = useSWR<PropertyPic[]>(
    `${BASE_URL}/property/property-pics/${nrPropertyId}/`,
    () => getAllListingPhotos<PropertyPic[]>(nrPropertyId),
    {
      fallbackData: initial,
    },
  );

  return {
    data,
    isLoading,
    isValidating,
    isError: error,
  };
}

export default useListingPhotos;
