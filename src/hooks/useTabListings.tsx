import { searchPropertyListings } from '@/services/server/property-search';
import { IPagination } from '@/types/common';
import { NRProperties } from '@/types/properties';

import useSWR from 'swr';

function useTabListings(tab: string) {
  const { data, isLoading, error, isValidating } = useSWR(
    tab ? `property/nrproperty-listing/?${tab}=true` : null,
    () =>
      searchPropertyListings<{ results: NRProperties[] } & IPagination>(
        1,
        tab ? `${tab}=true` : '',
      ),
    {
      revalidateIfStale: true,
    },
  );

  return {
    data,
    isLoading,
    isValidating,
    isError: error,
  };
}

export default useTabListings;
