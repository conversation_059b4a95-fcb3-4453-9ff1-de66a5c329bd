'use client';

import { useCallback, useEffect, useState } from 'react';

import { getRentalRatesForProperty } from '@/services/server/properties';
import { RentalRatesCalendarData } from '@/types/properties';

const usePropertyRentalRates = (
  propertyId: number,
  selectionStart: string,
  selectionEnd: string,
) => {
  const [data, setData] = useState<RentalRatesCalendarData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const fetchData = useCallback(() => {
    setIsLoading(true);
    getRentalRatesForProperty<RentalRatesCalendarData[]>(propertyId, selectionStart, selectionEnd)
      .then((_data) => {
        setData(_data);
        setIsLoading(false);
      })
      .catch((_error) => {
        console.log('failed to fetch Rates and Rules', _error);
        setIsLoading(false);
      });
  }, [propertyId, selectionEnd, selectionStart]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return { data, setData, isLoading, fetchData };
};

export default usePropertyRentalRates;
