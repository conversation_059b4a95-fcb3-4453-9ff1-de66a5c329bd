import React from 'react';

import { usePathname } from 'next/navigation';

const useIsAuthenticated = () => {
  const pathname = usePathname();
  const [isAuthenticated, setIsAuthenticated] = React.useState(false);
  React.useEffect(() => {
    fetch('/api/check-auth')
      .then((data) => data.json())
      .then(({ isAuthenticated: _isAuthenticated }) => {
        setIsAuthenticated(!!_isAuthenticated);
      })
      .catch((err) => console.log('error', err));
  }, [pathname]);
  return isAuthenticated;
};

export default useIsAuthenticated;
