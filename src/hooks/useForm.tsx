import { useCallback, useState } from 'react';

const generateDefaultPristine = <P,>(formState: any) => {
  let defaultPristine = {};
  Object.keys(formState).forEach((key) => {
    defaultPristine = {
      ...defaultPristine,
      [key]: true,
    };
  });

  return defaultPristine as P;
};

const removePristine = <P,>(formState: any) => {
  let defaultPristine = {};
  Object.keys(formState).forEach((key) => {
    defaultPristine = {
      ...defaultPristine,
      [key]: false,
    };
  });

  return defaultPristine as P;
};

export type FormValidator<T> = {
  [key in keyof T]?: (allValues: T, name: keyof T, value: any) => void;
};

/**
 *
 * A hook for FORM values, errors and validations
 * @param defaultValues Default values of all entries in the Form field interface
 * @param validator | A mapped form validator function for all entries in the Form fields interface
 * @param validateOnChange | Validate the form on change | default is true
 * @returns { formState, pristine, errors, onChange, preSubmitCheck }
 */
const useForm = <T,>(
  defaultValues: T,
  validator?: {
    [key in keyof T]?: (values: T, name: keyof T, value: any) => void;
  },
  validateOnChange = true,
) => {
  type Pristine = { [key in keyof T]: boolean };
  type Errors = { [key in keyof T]?: string };
  const [formState, setFormState] = useState(defaultValues);
  const [pristine, setPristine] = useState<Pristine>(generateDefaultPristine<Pristine>(formState));
  const [errors, setErrors] = useState<Errors>({});

  const onChange = useCallback(
    (value: any, name: string) => {
      if (validateOnChange) {
        const errorMessage =
          validator?.[name as keyof T]?.(formState, name as keyof T, value) ?? '';
        setErrors({
          ...errors,
          [name]: errorMessage,
        });
      }
      setFormState({
        ...formState,
        [name]: value,
      });
      setPristine({
        ...pristine,
        [name]: false,
      });
    },
    [errors, formState, pristine, validateOnChange, validator],
  );

  const preSubmitCheck = useCallback(() => {
    const newErrors = Object.keys(formState as any).reduce((acc: Errors, curr: string) => {
      const name = curr as keyof T;
      return {
        ...acc,
        [name]: validator?.[name]?.(formState, name, formState[name]) ?? '',
      };
    }, {});
    setErrors(newErrors);
    setPristine(removePristine<Pristine>(formState));
    return newErrors;
  }, [formState, validator]);

  const onResetForm = useCallback(() => {
    setFormState(defaultValues);
    setPristine(generateDefaultPristine<Pristine>(formState));
  }, [defaultValues, formState]);

  return { formState, pristine, errors, onChange, preSubmitCheck, onResetForm };
};

export default useForm;
