import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getOtherListings = async (calender: number, pageSize: number) => {
  try {
    const result = await axios({
      url: `${BASE_URL}/property/nrproperty-listing/?calendar_update=${calender}&page_size=${pageSize}`,
      method: 'GET',
    });
    return result.data;
  } catch (error) {
    console.error(error);
    return null;
  }
};
