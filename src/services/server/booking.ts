'use server';

import { AuthData, isValidJsonString, NR_ACCESS_COOKIE } from '@/utils/api-utils/cookie';
import { Nullable } from '@/utils/common';

import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';
const CNC_BASE_URL = process.env.NEXT_PUBLIC_CNC_API_BASE_URL || '';

export const checkBookingAvailability = async <T>(
  propertyId: number,
  adultsCount: number,
  childrenCount: number,
  from: string,
  to: string,
  bookingId?: number,
) => {
  const cookieStore = await cookies();
  const decryptedAuthString = Buffer.from(
    cookieStore.get(NR_ACCESS_COOKIE)?.value || '',
    'base64',
  ).toString();
  const decryptedAuthData: AuthData =
    isValidJsonString(decryptedAuthString) && JSON.parse(decryptedAuthString);

  const fetchURL = bookingId
    ? `/property/nrproperty-listing/check-booking-availability?nrPropertyId=${propertyId}&adultsCount=${adultsCount}&childrenCount=${childrenCount}&dateFrom=${from}&dateTo=${to}&bookingId=${bookingId}`
    : `/property/nrproperty-listing/check-booking-availability?nrPropertyId=${propertyId}&adultsCount=${adultsCount}&childrenCount=${childrenCount}&dateFrom=${from}&dateTo=${to}`;

  console.log('fetch URL', fetchURL);

  try {
    const res = await fetch(`${BASE_URL}${fetchURL}`, {
      headers: {
        Authorization: `Bearer ${decryptedAuthData.access_token}`,
      },
      cache: 'no-store',
      next: { tags: [fetchURL] },
    });

    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch listing booking availability data');
  }
};

type SubmitGetInTouchData = {
  phone?: string;
  email: string;
  first_name: string;
  last_name: string;
  comment: string;
  contact_method: string;
  neighborhood?: string;
  listing_id?: number;
  bedroom_number?: number;
  max_price?: number;
  guest?: number;
  children?: number;
  bedrooms?: number;
  property_address?: string;
  interest?: string;
  arrival_date?: string;
  departure_date?: string;
  pet_type?: string;
  pet_description?: string;
  pet_count?: number;
  flexibility?: Nullable<string>;
  source: string;
};

export const submitGetInTouch = async (data: SubmitGetInTouchData) => {
  const res = await fetch(`${CNC_BASE_URL}/get-in-touch`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Booking Request');
  }

  return res.json();
};

export type GuestLeasePayload = {
  rent: number;
  listing_id: number;
  arrival_date: string;
  departure_date: string;
  payment_method: 'ach' | 'credit_card';
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  address: string;
  adult_count: number;
  child_count: number;
  bringing_pets: boolean;
  pet_type?: Nullable<string>;
  pet_count?: Nullable<number>;
  pet_description?: Nullable<string>;
  travel_insurance: Nullable<boolean>;
  return_url: string;
  text: string;
  source: string;
};

export const createGuestLease = async (data: GuestLeasePayload) => {
  const res = await fetch(`${CNC_BASE_URL}/guest-leases`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Booking Request');
  }

  return res.json();
};

type BuyerInterestPayload = {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  timeframe?: Nullable<string>;
  budget_range?: Nullable<string>;
  send_similar_homes?: boolean;
  listing: Nullable<number>;
  source: string;
};

export const submitBuyerInterest = async (data: BuyerInterestPayload) => {
  const res = await fetch(`${CNC_BASE_URL}/buyer-interest`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to Submit Buyer Interest Request');
  }

  return res.json();
};
