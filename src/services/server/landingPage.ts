'use server';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getLandingPageContent = async <T>() => {
  const res = await fetch(`${BASE_URL}/admin/homepage-content/`, {
    next: {
      revalidate: 60,
      tags: [`homepage-content`],
    },
  });

  if (!res.ok) {
    throw new Error('Failed to fetch landing page contents');
  }

  return res.json() as T;
};

export const getLandingPageLinks = async <T>() => {
  const res = await fetch(`${BASE_URL}/admin/homepage-links/`, {
    next: {
      revalidate: 60,
      tags: [`homepage-links`],
    },
  });

  if (!res.ok) {
    throw new Error('Failed to fetch landing page links');
  }

  return res.json() as T;
};

export const getLandingPagePopularSearches = async <T>() => {
  const res = await fetch(`${BASE_URL}/admin/popular-searches/`, {
    next: {
      revalidate: 60,
      tags: [`popular-searches`],
    },
  });

  if (!res.ok) {
    throw new Error('Failed to fetch landing page popular searches');
  }

  return res.json() as T;
};
