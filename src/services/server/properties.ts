'use server';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getPropertyDetailsBySlug = async <T>(slug: string) => {
  const res = await fetch(`${BASE_URL}/property/nrproperty-listing/${slug}`, {
    next: {
      tags: [`property-details-${slug}`],
    },
  });

  if (!res.ok) {
    console.debug(`Failed to fetch property details page for slug -${slug}`);
    return null;
  }

  return res.json() as T;
};

export const getAllListingPhotos = async <T>(nrPropertyId: number) => {
  const res = await fetch(`${BASE_URL}/property/property-pics/${nrPropertyId}/`, {
    next: {
      tags: [`property-property-pic-${nrPropertyId}`],
    },
  });

  if (!res.ok) {
    throw new Error(`Failed to fetch property pics for ID -${nrPropertyId}`);
  }

  return res.json() as T;
};

export const getRentalRatesForProperty = async <T>(
  propertyId: number,
  startDate: string,
  endDate: string,
) => {
  const res = await fetch(
    `${BASE_URL}/property/rentalrate/?nightlyRate=1&nrPropertyId=${propertyId}&startDate=${startDate}&endDate=${endDate}`,
    {
      next: {
        revalidate: 0,
        tags: [`rentalRates-${propertyId}-startDate=${startDate}&endDate=${endDate}}`],
      },
    },
  );

  if (!res.ok) {
    throw new Error('Failed to fetch rental rate for property');
  }

  return res.json() as T;
};

export const getListingReviews = async <T>(propertyId: number) => {
  const res = await fetch(`${BASE_URL}/property/reviews/?nrPropertyId=${propertyId}`, {
    next: {
      revalidate: 0,
      tags: [`listing-reviews-${propertyId}`],
    },
  });

  if (!res.ok) {
    throw new Error('Failed to fetch rental rate for property');
  }

  return res.json() as T;
};
