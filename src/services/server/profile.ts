'use server';

import { AuthData, isValidJsonString, NR_ACCESS_COOKIE } from '@/utils/api-utils/cookie';

import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const getUserProfile = async <T>() => {
  const cookieStore = await cookies();
  const decryptedAuthString = Buffer.from(
    cookieStore.get(NR_ACCESS_COOKIE)?.value || '',
    'base64',
  ).toString();
  const decryptedAuthData: AuthData =
    isValidJsonString(decryptedAuthString) && JSON.parse(decryptedAuthString);

  try {
    const res = await fetch(`${BASE_URL}/user-profile/`, {
      headers: {
        Authorization: `Bearer ${decryptedAuthData.access_token}`,
      },
      cache: 'no-store',
      next: { revalidate: 0, tags: ['user-profile'] },
    });

    return res.json() as T;
  } catch (error) {
    console.log('error', error);
    throw new Error('Failed to fetch user profile');
  }
};
