const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const generateOTP = async (
  data:
    | { phone_number: string; user_id: number; signup_flow?: boolean }
    | { email: string; user_id: number; signup_flow?: boolean },
) => {
  const res = await fetch(`${BASE_URL}/generate/otp/`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!res.ok) {
    throw new Error('Failed to generate OTP');
  }

  return res.json();
};

export const verifyOTP = async <T>(data: { otp: number; user_id: number; email?: string }) => {
  const res = await fetch(`${BASE_URL}/verify/otp/`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });

  // if (!res.ok) {
  //   throw new Error('Failed to VERIFY OTP');
  // }

  return res.json() as T;
};
