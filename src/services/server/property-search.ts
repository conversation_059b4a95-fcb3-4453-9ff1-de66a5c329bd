'use server';

import { AuthData, isValidJsonString, NR_ACCESS_COOKIE } from '@/utils/api-utils/cookie';

import { cookies } from 'next/headers';

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

export const searchPropertyListings = async <T>(page = 1, queries = '') => {
  // console.log('queries ====> searchPropertyListings', queries, page);
  const cookieStore = await cookies();
  const decryptedAuthString = Buffer.from(
    cookieStore.get(NR_ACCESS_COOKIE)?.value || '',
    'base64',
  ).toString();
  const decryptedAuthData: AuthData =
    isValidJsonString(decryptedAuthString) && JSON.parse(decryptedAuthString);
  let headers = {};

  if (decryptedAuthData.access_token) {
    headers = {
      Authorization: `Bearer ${decryptedAuthData.access_token}`,
    };
  }
  try {
    const result = await fetch(
      `${BASE_URL}/property/nrproperty-listing/?page=${page}${
        queries.trim() !== '' ? `&${queries}` : ''
      }`,
      {
        headers,
        next: { revalidate: 10, tags: [`property-listings-${queries}`] },
      },
    );
    return result.json() as T;
  } catch (error) {
    return null;
  }
};

export const getAllNeighborhoods = async <T>() => {
  const res = await fetch(`${BASE_URL}/admin/neighborhood/`, {
    next: {
      tags: [`admin-neighborhood`],
    },
  });

  if (!res.ok) {
    throw new Error(`Failed to fetch neighborhoods`);
  }

  return res.json() as T;
};
