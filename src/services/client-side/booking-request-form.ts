import { handleAPIRequests } from '@/middlewares/api';
import { BookingPaymentMethod } from '@/types/booking';

export type BookingRequestFormPaylod = {
  nrPropertyId: number;
  checkInDateTime: string;
  checkOutDateTime: string;
  adultsCount: number;
  childrenCount: number;
  totalGuestCount: number;
  rent: number;
  serviceFee: number;
  text: string;
  paymentMethod?: BookingPaymentMethod;
  communityServiceId?: number;
  petsAllowed: boolean;
  couponCode?: string;
  return_url?: string;
  travelInsurance?: boolean;
  petCount?: number;
  petType?: string;
  petDescription?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
};

export const sendBookingRequestForm = (data: BookingRequestFormPaylod) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/booking/booking/',
    data: data,
  });
};
