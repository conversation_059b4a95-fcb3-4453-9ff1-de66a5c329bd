import { handleAPIRequests } from '@/middlewares/api';

export const actionSubmitRegisterData = (data: any) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/signup/',
    data,
  });
};

export const actionGoogleLogin = (data: { access_token: string }) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/google/',
    data,
  });
};

export const actionGoogleSignup = (data: { access_token: string; user_type: number }) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/google/',
    data,
  });
};

export const actionLogin = (data: { username: string; password: string }) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/signin/',
    data,
  });
};

export const actionRequestOTP = (
  data:
    | { phone_number: string; user_id: number; signup_flow?: boolean }
    | { email: string; user_id: number; signup_flow?: boolean },
) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/generate/otp/',
    data,
  });
};

export const actionVerifyOTP = (
  data:
    | { otp: number; user_id: number; email: string }
    | { otp: number; user_id: number; phone_number: string },
) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/verify/otp/',
    data,
  });
};
