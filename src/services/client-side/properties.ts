import { handleAPIRequests } from '@/middlewares/api';
import { EntityID } from '@/redux/interfaces';

export const getPropertyDetails = <T>(propertyId: EntityID) => {
  return handleAPIRequests({
    method: 'get',
    endpoint: `/property/nrproperty-listing/details/${propertyId}`,
  }) as Promise<T>;
};

export const togglePropertyFavorite = (nrPropertyId: number) => {
  return handleAPIRequests({
    method: 'post',
    endpoint: '/guest/favourite-property/',
    authenticated: true,
    data: { nrPropertyId },
  });
};
