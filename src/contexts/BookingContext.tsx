'use client';

import { createContext, useContext, useEffect, useRef, useState } from 'react';

import { DateRange } from 'react-day-picker';
import { toast } from 'react-hot-toast';

import { GuestsValues } from '@/clients/components/common/GuestSelector';
import { checkBookingAvailability } from '@/services/server/booking';
import { BookingAvailabilityData, BookingPaymentMethod } from '@/types/booking';
import { Nullable, ProgressStatus } from '@/types/common';
import { ProfileData } from '@/types/profile';
import { PetType, PropertyDetails } from '@/types/properties';
import { parseDateString } from '@/utils/common';
import {
  EnhacedEcomPropertyItem,
  formatSeoProperty,
  pushEcommerceDataLayer,
} from '@/utils/enhancedEcomAnanalytics';

import { format } from 'date-fns';

export enum BookingFlowStep {
  DETAILS = 'DETAILS',
  REGISTER = 'REGISTER',
  BILLING_DETAILS = 'BILLING_DETAILS',
}

export type GuestData = {
  firstname: string;
  lastname: string;
  phone: string;
  userLoginEmail: string;
};

type BookingContextType = {
  isInsuranceAdded: boolean;
  setIsInsuranceAdded: (status: boolean) => void;
  paymentMethod: Nullable<BookingPaymentMethod>;
  setPaymentMethod: (method: Nullable<BookingPaymentMethod>) => void;
  errors: { [key: string]: Nullable<string> };
  setErrors: (err: any) => void;
  progressStatus: Nullable<ProgressStatus>;
  setProgressStatus: (_p: Nullable<ProgressStatus>) => void;
  step: Nullable<BookingFlowStep>;
  setStep: (step: Nullable<BookingFlowStep>) => void;
  userData?: ProfileData;
  date?: DateRange;
  setDate: (date?: DateRange) => void;
  guests: GuestsValues;
  setGuests: (guests: GuestsValues) => void;
  petCount: number;
  setPetCount: (count: number) => void;
  petType: PetType;
  setPetType: (type: PetType) => void;
  isPetSelected: boolean;
  setIsPetSelected: (selected: boolean) => void;
  petDescription: string;
  setPetDescription: (desc: string) => void;
  bookingAvailabilityData: Nullable<BookingAvailabilityData>;
  setBookingAvailabilityData: (data: Nullable<BookingAvailabilityData>) => void;
  datePickerError: Nullable<string>;
  setDatePickerError: (err: Nullable<string>) => void;
  isFetchingBookingDetails: boolean;
  setIsFetchingBookingDetails: (status: boolean) => void;
  guestData: Nullable<GuestData>;
  setGuestData: (data: Nullable<GuestData>) => void;
  docusignUrl: string;
  setDocusignUrl: (url: string) => void;
};

const BookingContext = createContext<BookingContextType | undefined>(undefined);

type SearchParamsType = {
  adults?: string;
  children?: string;
  from?: string;
  to?: string;
  petCount?: string;
  petType?: string;
  petDescription?: string;
};

type BookingContextProviderProps = {
  children: React.ReactNode;
  userData?: ProfileData;
  nrPropertyId: number;
  property: PropertyDetails;
  searchParams?: SearchParamsType;
};

export const BookingContextProvider = ({
  children,
  userData,
  nrPropertyId,
  property,
  searchParams,
}: BookingContextProviderProps) => {
  const [errors, setErrors] = useState<{ [key: string]: Nullable<string> }>({});
  const [isInsuranceAdded, setIsInsuranceAdded] = useState<boolean>(false);
  const [paymentMethod, setPaymentMethod] = useState<Nullable<BookingPaymentMethod>>(null);
  const [progressStatus, setProgressStatus] = useState<Nullable<ProgressStatus>>(null);
  const [step, setStep] = useState<Nullable<BookingFlowStep>>(null);
  const [date, setDate] = useState<DateRange | undefined>(undefined);
  const [guests, setGuests] = useState<GuestsValues>({ adults: 1, children: 0 });
  const [petCount, setPetCount] = useState<number>(1);
  const [petType, setPetType] = useState<PetType>(PetType.DOG);
  const [isPetSelected, setIsPetSelected] = useState<boolean>(false);
  const [petDescription, setPetDescription] = useState<string>('');
  const [bookingAvailabilityData, setBookingAvailabilityData] = useState<
    Nullable<BookingAvailabilityData>
  >(null);
  const [datePickerError, setDatePickerError] = useState<Nullable<string>>(null);
  const [isFetchingBookingDetails, setIsFetchingBookingDetails] = useState<boolean>(false);
  const [isClientSide, setIsClientSide] = useState<boolean>(false);
  const [guestData, setGuestData] = useState<Nullable<GuestData>>(null);

  const [docusignUrl, setDocusignUrl] = useState<string>('');

  const dateRef = useRef(date);

  // Set isClientSide to true after component mounts
  useEffect(() => {
    setIsClientSide(true);
  }, []);

  // Initialize all state values from searchParams after component mounts
  useEffect(() => {
    if (isClientSide && searchParams) {
      // Set guests
      setGuests({
        adults: searchParams.adults ? Number(searchParams.adults) : 1,
        children: searchParams.children ? Number(searchParams.children) : 0,
      });

      // Set pet-related state
      setPetCount(searchParams.petCount ? Number(searchParams.petCount) : 1);
      setPetType(searchParams.petType ? (searchParams.petType as PetType) : PetType.DOG);
      setIsPetSelected(!!(searchParams.petCount && Number(searchParams.petCount) > 0));
      setPetDescription(searchParams.petDescription ?? '');

      // Set date range
      if (searchParams.from && searchParams.to) {
        setDate({
          from: parseDateString(searchParams.from),
          to: parseDateString(searchParams.to),
        });
      }
    }
  }, [isClientSide, searchParams]);

  useEffect(() => {
    if (!date?.from || !date?.to) {
      setBookingAvailabilityData(null);
      setIsFetchingBookingDetails(false);
      return;
    }

    dateRef.current = date;

    const NOT_AVAILABLE_MSG = 'Not available for given dates.';

    const fetchBookingAvailability = async (from: Date, to: Date) => {
      setDatePickerError(null);
      setIsFetchingBookingDetails(true);

      try {
        const _data = await checkBookingAvailability<BookingAvailabilityData>(
          nrPropertyId,
          1,
          0,
          format(from, 'yyyy-MM-dd'),
          format(to, 'yyyy-MM-dd'),
        );

        if (!_data.rent || !_data.total) {
          const errorMsg = (_data as any)?.details ?? NOT_AVAILABLE_MSG;
          toast.error(errorMsg);
          setDatePickerError(errorMsg);
          setBookingAvailabilityData(null);
          setIsFetchingBookingDetails(false);
          return;
        }

        if (dateRef.current?.from && dateRef.current?.to) {
          console.log('the data is', _data);
          setBookingAvailabilityData(_data);
          setIsFetchingBookingDetails(false);
        }
      } catch (error) {
        console.error(error);
        setBookingAvailabilityData(null);
        setIsFetchingBookingDetails(false);
      }
    };

    const handler = setTimeout(() => {
      if (date?.from && date?.to) {
        fetchBookingAvailability(date.from, date.to);
      }
    }, 500); // debounce

    return () => clearTimeout(handler);
  }, [date, nrPropertyId]);

  useEffect(() => {
    const propertyEcomAnalyticsData = [
      formatSeoProperty((property as unknown) as EnhacedEcomPropertyItem),
    ];

    pushEcommerceDataLayer('view_cart', propertyEcomAnalyticsData);
  }, [property]);

  return (
    <BookingContext.Provider
      value={{
        isInsuranceAdded,
        setIsInsuranceAdded,
        paymentMethod,
        setPaymentMethod,
        errors,
        setErrors,
        progressStatus,
        setProgressStatus,
        step,
        setStep,
        userData,
        date,
        setDate,
        guests,
        setGuests,
        petCount,
        setPetCount,
        petType,
        setPetType,
        isPetSelected,
        setIsPetSelected,
        petDescription,
        setPetDescription,
        bookingAvailabilityData,
        setBookingAvailabilityData,
        datePickerError,
        setDatePickerError,
        isFetchingBookingDetails,
        setIsFetchingBookingDetails,
        guestData,
        setGuestData,
        docusignUrl,
        setDocusignUrl,
      }}
    >
      {children}
    </BookingContext.Provider>
  );
};

export const useBooking = () => {
  const context = useContext(BookingContext);
  if (!context) {
    throw new Error('useBooking must be used within a BookingFormContextProvider');
  }
  return context;
};
