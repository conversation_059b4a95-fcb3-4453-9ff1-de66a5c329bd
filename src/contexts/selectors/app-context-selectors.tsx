import { useAppContext } from '@/hooks/useAppContext';

export const useLoginDialog = () => {
  const { showLoginDialog, onToggleLoginDialog } = useAppContext();
  return { showLoginDialog, onToggleLoginDialog };
};

export const useProfile = () => {
  const { profileData, setProfileData } = useAppContext();
  return { profileData, setProfileData };
};

export const useUserType = () => {
  const { userType, setUserType } = useAppContext();
  return { userType, setUserType };
};

export const useIsFetchingProfile = () => {
  const { isFetchingProfile, setIsFetchingProfile } = useAppContext();
  return { isFetchingProfile, setIsFetchingProfile };
};
