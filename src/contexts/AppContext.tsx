'use client';

import {
  Dispatch,
  ReactNode,
  SetStateAction,
  createContext,
  useCallback,
  useMemo,
  useState,
} from 'react';

import LoadingSpinner from '@/components/ui/loading-spinner';
import { NR_USER_TYPES, ProfileData } from '@/types/profile';

import dynamic from 'next/dynamic';

const GoogleOAuthProvider = dynamic(
  () => import('@react-oauth/google').then((mod) => mod.GoogleOAuthProvider),
  { ssr: false },
);

const LoginDialogWrapper = dynamic(
  () => import('@/clients/components/common/AuthenticationDialog/LoginDialogWrapper'),
  {
    ssr: false,
    loading: () => (
      <div className="fixed bg-black/40 inset-0 flex items-center justify-center text-white">
        <LoadingSpinner className="w-10 h-10" />
      </div>
    ),
  },
);

type Context = {
  showLoginDialog: boolean;
  onToggleLoginDialog: () => void;
  isFetchingProfile: boolean;
  setIsFetchingProfile: Dispatch<SetStateAction<boolean>>;
  profileData?: ProfileData;
  setProfileData: Dispatch<SetStateAction<ProfileData | undefined>>;
  userType?: NR_USER_TYPES;
  setUserType: Dispatch<SetStateAction<NR_USER_TYPES | undefined>>;
};

const GOOGLE_CLIENT_ID = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID ?? '';

if (!GOOGLE_CLIENT_ID) {
  console.warn('⚠️ Google Client ID is missing in environment variables.');
}

export const AppContext = createContext<Context>({} as Context);

type ContextProps = {
  children: ReactNode;
};

const AppContextContainer = ({ children }: ContextProps) => {
  const [showLoginDialog, setShowLoginDialog] = useState<boolean>(false);
  const [isFetchingProfile, setIsFetchingProfile] = useState<boolean>(false);
  const [profileData, setProfileData] = useState<ProfileData | undefined>(undefined);
  const [userType, setUserType] = useState<NR_USER_TYPES | undefined>(undefined);

  const onToggleLoginDialog = useCallback(() => {
    setShowLoginDialog((prev) => !prev);
  }, []);

  const contextValue = useMemo(
    () => ({
      showLoginDialog,
      onToggleLoginDialog,
      isFetchingProfile,
      setIsFetchingProfile,
      profileData,
      setProfileData,
      userType,
      setUserType,
    }),
    [showLoginDialog, onToggleLoginDialog, isFetchingProfile, profileData, userType],
  );

  return (
    <AppContext.Provider value={contextValue}>
      {children}
      {showLoginDialog && (
        <GoogleOAuthProvider clientId={GOOGLE_CLIENT_ID}>
          <LoginDialogWrapper open={showLoginDialog} onToggle={onToggleLoginDialog} />
        </GoogleOAuthProvider>
      )}
    </AppContext.Provider>
  );
};

export default AppContextContainer;
