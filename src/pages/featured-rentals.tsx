import React from 'react';

import { FEATURED_LISTINGS_ROUTE } from '@/constants/routes';
import usePageViewLogger from '@/hooks/usePageViewLogger';
import { NRProperties, Pagination as PaginationInterface } from '@/redux/interfaces';
import { getPropertyListingsInstagram } from '@/services/properties';
import FeaturedRentals from '@/views/FeaturedRentals';

import { GetServerSidePropsContext } from 'next';
import Head from 'next/head';

type PageProps = {
  listingsData: { results: NRProperties[] } & PaginationInterface;
};

const FeaturedListingsPage = ({ listingsData }: PageProps) => {
  usePageViewLogger();
  return (
    <>
      <Head>
        <title>Featured Listings</title>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"
        ></meta>
      </Head>
      <FeaturedRentals listingsData={listingsData} />
    </>
  );
};

export async function getServerSideProps(ctx: GetServerSidePropsContext) {
  const { page = 1 } = ctx?.query;
  let listingsData;
  try {
    listingsData = await getPropertyListingsInstagram(Number(page));
  } catch (error) {
    console.log('error', error);
  }

  if (!listingsData && Number(page) !== 1) {
    return {
      redirect: {
        permanent: true,
        destination: FEATURED_LISTINGS_ROUTE,
      },
    };
  }
  return {
    props: {
      listingsData,
    },
  };
}

export default FeaturedListingsPage;
